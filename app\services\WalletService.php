<?php

namespace App\Services;

use App\Core\Database;

/**
 * Wallet Service
 * সব ফিচারের জন্য কমন ওয়ালেট সার্ভিস
 */
class WalletService
{
    private $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Get user wallet balance
     */
    public function getBalance($userId)
    {
        $wallet = $this->db->fetch(
            "SELECT balance FROM wallets WHERE user_id = ?",
            [$userId]
        );

        return $wallet ? (float) $wallet['balance'] : 0.00;
    }

    /**
     * Add money to wallet (for any feature)
     */
    public function addMoney($userId, $amount, $source, $description = '', $referenceId = null)
    {
        if ($amount <= 0) {
            throw new \Exception('Amount must be greater than 0');
        }

        $this->db->beginTransaction();

        try {
            // Update wallet balance
            $currentBalance = $this->getBalance($userId);
            $newBalance = $currentBalance + $amount;

            $this->db->update('wallets', [
                'balance' => $newBalance,
                'updated_at' => date('Y-m-d H:i:s')
            ], 'user_id = ?', [$userId]);

            // Record transaction
            $this->recordTransaction($userId, 'credit', $amount, $source, $description, $referenceId);

            $this->db->commit();

            return [
                'success' => true,
                'previous_balance' => $currentBalance,
                'new_balance' => $newBalance,
                'amount_added' => $amount
            ];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * Deduct money from wallet
     */
    public function deductMoney($userId, $amount, $source, $description = '', $referenceId = null)
    {
        if ($amount <= 0) {
            throw new \Exception('Amount must be greater than 0');
        }

        $currentBalance = $this->getBalance($userId);
        
        if ($currentBalance < $amount) {
            throw new \Exception('Insufficient balance');
        }

        $this->db->beginTransaction();

        try {
            // Update wallet balance
            $newBalance = $currentBalance - $amount;

            $this->db->update('wallets', [
                'balance' => $newBalance,
                'updated_at' => date('Y-m-d H:i:s')
            ], 'user_id = ?', [$userId]);

            // Record transaction
            $this->recordTransaction($userId, 'debit', $amount, $source, $description, $referenceId);

            $this->db->commit();

            return [
                'success' => true,
                'previous_balance' => $currentBalance,
                'new_balance' => $newBalance,
                'amount_deducted' => $amount
            ];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * Record transaction in database
     */
    private function recordTransaction($userId, $type, $amount, $source, $description, $referenceId)
    {
        $this->db->insert('transactions', [
            'user_id' => $userId,
            'type' => $type, // credit or debit
            'amount' => $amount,
            'source' => $source, // quiz, social, withdrawal, etc.
            'description' => $description,
            'reference_id' => $referenceId,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Get user transactions
     */
    public function getTransactions($userId, $limit = 50, $offset = 0)
    {
        return $this->db->fetchAll(
            "SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?",
            [$userId, $limit, $offset]
        );
    }

    /**
     * Quiz reward system
     */
    public function addQuizReward($userId, $quizId, $score, $totalQuestions)
    {
        // Calculate reward based on score
        $percentage = ($score / $totalQuestions) * 100;
        $baseReward = 10; // Base reward amount
        
        if ($percentage >= 90) {
            $reward = $baseReward * 2; // Double reward for 90%+
        } elseif ($percentage >= 70) {
            $reward = $baseReward * 1.5; // 1.5x reward for 70%+
        } elseif ($percentage >= 50) {
            $reward = $baseReward; // Base reward for 50%+
        } else {
            $reward = 0; // No reward below 50%
        }

        if ($reward > 0) {
            return $this->addMoney(
                $userId, 
                $reward, 
                'quiz', 
                "Quiz completed with {$percentage}% score", 
                $quizId
            );
        }

        return ['success' => false, 'message' => 'No reward earned'];
    }

    /**
     * Social media reward system
     */
    public function addSocialReward($userId, $action, $referenceId = null)
    {
        $rewards = [
            'post_create' => 5,    // 5 taka for creating post
            'post_like' => 1,      // 1 taka for liking post
            'comment' => 2,        // 2 taka for commenting
            'share' => 3,          // 3 taka for sharing
            'follow' => 2          // 2 taka for following someone
        ];

        $reward = $rewards[$action] ?? 0;

        if ($reward > 0) {
            return $this->addMoney(
                $userId, 
                $reward, 
                'social', 
                "Social action: {$action}", 
                $referenceId
            );
        }

        return ['success' => false, 'message' => 'No reward for this action'];
    }

    /**
     * Transfer money between users
     */
    public function transfer($fromUserId, $toUserId, $amount, $description = '')
    {
        if ($amount <= 0) {
            throw new \Exception('Amount must be greater than 0');
        }

        if ($fromUserId === $toUserId) {
            throw new \Exception('Cannot transfer to same user');
        }

        $this->db->beginTransaction();

        try {
            // Deduct from sender
            $this->deductMoney($fromUserId, $amount, 'transfer_out', "Transfer to user {$toUserId}: {$description}");

            // Add to receiver
            $this->addMoney($toUserId, $amount, 'transfer_in', "Transfer from user {$fromUserId}: {$description}");

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'Transfer completed successfully'
            ];

        } catch (\Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * Withdraw money from wallet
     */
    public function withdraw($userId, $amount, $method = 'bank', $details = [])
    {
        if ($amount < 100) { // Minimum withdrawal amount
            throw new \Exception('Minimum withdrawal amount is 100 taka');
        }

        $result = $this->deductMoney(
            $userId,
            $amount,
            'withdrawal',
            "Withdrawal via {$method}"
        );

        // Here you can integrate with payment gateway
        // For now, just record the withdrawal request
        $this->db->insert('withdrawal_requests', [
            'user_id' => $userId,
            'amount' => $amount,
            'method' => $method,
            'details' => json_encode($details),
            'status' => 'pending',
            'created_at' => date('Y-m-d H:i:s')
        ]);

        return $result;
    }

    /**
     * Get wallet statistics
     */
    public function getWalletStats($userId)
    {
        $stats = $this->db->fetch(
            "SELECT
                COUNT(*) as total_transactions,
                SUM(CASE WHEN type = 'credit' THEN amount ELSE 0 END) as total_earned,
                SUM(CASE WHEN type = 'debit' THEN amount ELSE 0 END) as total_spent,
                SUM(CASE WHEN source = 'quiz' AND type = 'credit' THEN amount ELSE 0 END) as quiz_earnings,
                SUM(CASE WHEN source = 'social' AND type = 'credit' THEN amount ELSE 0 END) as social_earnings
            FROM transactions
            WHERE user_id = ?",
            [$userId]
        );

        return [
            'current_balance' => $this->getBalance($userId),
            'total_transactions' => (int) $stats['total_transactions'],
            'total_earned' => (float) $stats['total_earned'],
            'total_spent' => (float) $stats['total_spent'],
            'quiz_earnings' => (float) $stats['quiz_earnings'],
            'social_earnings' => (float) $stats['social_earnings']
        ];
    }

    /**
     * Check if user can afford amount
     */
    public function canAfford($userId, $amount)
    {
        return $this->getBalance($userId) >= $amount;
    }

    /**
     * Get recent transactions for feed
     */
    public function getRecentTransactionsForFeed($userId, $limit = 10)
    {
        return $this->db->fetchAll(
            "SELECT type, amount, source, description, created_at
            FROM transactions
            WHERE user_id = ? AND type = 'credit'
            ORDER BY created_at DESC
            LIMIT ?",
            [$userId, $limit]
        );
    }
}
