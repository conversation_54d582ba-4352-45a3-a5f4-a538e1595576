<?php
/**
 * Services Section - QuizSpace Earning Methods
 * Professional showcase of earning opportunities
 */

// Dynamic services data
$servicesData = [
    'title' => 'Multiple Ways to Earn Money',
    'subtitle' => 'Discover all the exciting ways you can earn coins and cash on QuizSpace',
    'services' => [
        [
            'icon' => 'fas fa-brain',
            'title' => 'Quiz Challenges',
            'description' => 'Answer engaging quizzes on various topics and earn coins for every correct answer. The more you know, the more you earn!',
            'features' => [
                'Multiple difficulty levels',
                'Daily new quizzes',
                'Bonus for streaks',
                'Topic-based categories'
            ],
            'earning_potential' => '৳50-200/day',
            'color' => 'primary',
            'cta_text' => 'Start Quiz',
            'cta_link' => '/quiz'
        ],
        [
            'icon' => 'fas fa-share-alt',
            'title' => 'Social Activities',
            'description' => 'Engage with our community through likes, shares, comments, and posts. Build your network while earning money!',
            'features' => [
                'Share & earn coins',
                'Comment rewards',
                'Post engagement bonus',
                'Referral commissions'
            ],
            'earning_potential' => '৳30-150/day',
            'color' => 'success',
            'cta_text' => 'Join Community',
            'cta_link' => '/community'
        ],
        [
            'icon' => 'fas fa-trophy',
            'title' => 'Competitions',
            'description' => 'Participate in daily, weekly, and monthly competitions. Compete with others and win big prizes and cash rewards!',
            'features' => [
                'Daily tournaments',
                'Weekly challenges',
                'Monthly mega contests',
                'Leaderboard rewards'
            ],
            'earning_potential' => '৳100-1000/week',
            'color' => 'warning',
            'cta_text' => 'View Contests',
            'cta_link' => '/competitions'
        ],
        [
            'icon' => 'fas fa-gift',
            'title' => 'Daily Bonuses',
            'description' => 'Login daily to claim your bonus coins. Maintain streaks for bigger rewards and unlock special promotional offers!',
            'features' => [
                'Daily login bonus',
                'Streak multipliers',
                'Special promotions',
                'Surprise rewards'
            ],
            'earning_potential' => '৳20-100/day',
            'color' => 'info',
            'cta_text' => 'Claim Bonus',
            'cta_link' => '/bonuses'
        ],
        [
            'icon' => 'fas fa-users',
            'title' => 'Referral Program',
            'description' => 'Invite friends and family to join QuizSpace. Earn commission from their activities and build your passive income stream!',
            'features' => [
                '10% lifetime commission',
                'Multi-level referrals',
                'Bonus for active referrals',
                'Monthly referral contests'
            ],
            'earning_potential' => '৳500-5000/month',
            'color' => 'danger',
            'cta_text' => 'Invite Friends',
            'cta_link' => '/referral'
        ],
        [
            'icon' => 'fas fa-ad',
            'title' => 'Ad Viewing',
            'description' => 'Watch short advertisements and earn coins. Simple, easy, and rewarding way to make money in your free time!',
            'features' => [
                'Short video ads',
                'Skip after 5 seconds',
                'Multiple ad networks',
                'Daily ad limits'
            ],
            'earning_potential' => '৳10-50/day',
            'color' => 'dark',
            'cta_text' => 'Watch Ads',
            'cta_link' => '/ads'
        ]
    ]
];
?>

<!-- Services Section -->
<section class="py-5 bg-dark text-white" id="services">
    <div class="container">
        <!-- Section Header -->
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="display-5 fw-bold mb-3"><?= htmlspecialchars($servicesData['title']) ?></h2>
                <p class="lead opacity-90"><?= htmlspecialchars($servicesData['subtitle']) ?></p>
                <div class="d-flex justify-content-center">
                    <div class="bg-warning" style="width: 60px; height: 4px; border-radius: 2px;"></div>
                </div>
            </div>
        </div>

        <!-- Services Grid -->
        <div class="row g-4">
            <?php foreach ($servicesData['services'] as $index => $service): ?>
            <div class="col-lg-4 col-md-6">
                <div class="service-card h-100 bg-white bg-opacity-5 rounded-4 p-4 border border-white border-opacity-10 position-relative overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="position-absolute top-0 end-0 opacity-5">
                        <i class="<?= $service['icon'] ?>" style="font-size: 4rem; color: var(--bs-<?= $service['color'] ?>);"></i>
                    </div>

                    <!-- Content -->
                    <div class="position-relative">
                        <!-- Header -->
                        <div class="d-flex align-items-center mb-3">
                            <div class="service-icon me-3">
                                <div class="d-inline-flex align-items-center justify-content-center rounded-circle bg-<?= $service['color'] ?> bg-opacity-20 p-3">
                                    <i class="<?= $service['icon'] ?> text-<?= $service['color'] ?>" style="font-size: 1.5rem;"></i>
                                </div>
                            </div>
                            <div>
                                <h5 class="fw-bold mb-1"><?= htmlspecialchars($service['title']) ?></h5>
                                <span class="badge bg-<?= $service['color'] ?> bg-opacity-20 text-<?= $service['color'] ?> small">
                                    <?= htmlspecialchars($service['earning_potential']) ?>
                                </span>
                            </div>
                        </div>

                        <!-- Description -->
                        <p class="text-white-50 mb-4 lh-base"><?= htmlspecialchars($service['description']) ?></p>

                        <!-- Features -->
                        <ul class="list-unstyled mb-4">
                            <?php foreach ($service['features'] as $feature): ?>
                            <li class="d-flex align-items-center mb-2">
                                <i class="fas fa-check text-<?= $service['color'] ?> me-2 small"></i>
                                <small class="text-white-50"><?= htmlspecialchars($feature) ?></small>
                            </li>
                            <?php endforeach; ?>
                        </ul>

                        <!-- CTA Button -->
                        <a href="<?= htmlspecialchars($service['cta_link']) ?>" class="btn btn-<?= $service['color'] ?> btn-sm w-100">
                            <?= htmlspecialchars($service['cta_text']) ?> <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>

                    <!-- Hover Effect -->
                    <div class="service-overlay position-absolute top-0 start-0 w-100 h-100 bg-<?= $service['color'] ?> bg-opacity-5 opacity-0 transition-all"></div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Bottom CTA -->
        <div class="row justify-content-center mt-5">
            <div class="col-lg-8 text-center">
                <div class="bg-white bg-opacity-10 rounded-4 p-4">
                    <h4 class="fw-bold mb-3">Ready to Start Earning?</h4>
                    <p class="opacity-90 mb-4">Join thousands of users already making money with multiple earning methods</p>
                    <div class="d-flex flex-wrap justify-content-center gap-3">
                        <a href="/register" class="btn btn-warning btn-lg px-4">
                            <i class="fas fa-rocket me-2"></i>Join Now - It's Free
                        </a>
                        <a href="/about" class="btn btn-outline-light btn-lg px-4">
                            <i class="fas fa-info-circle me-2"></i>Learn More
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section Styles -->
<style>
.service-card {
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.3);
    border-color: rgba(255,255,255,0.2) !important;
}

.service-card:hover .service-overlay {
    opacity: 1 !important;
}

.service-icon {
    transition: transform 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1);
}

.transition-all {
    transition: all 0.3s ease;
}

/* Animation for service cards */
.service-card {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

<?php foreach ($servicesData['services'] as $index => $service): ?>
.service-card:nth-child(<?= $index + 1 ?>) {
    animation-delay: <?= $index * 0.1 ?>s;
}
<?php endforeach; ?>

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .service-card {
        margin-bottom: 1rem;
    }
}
</style>