/**
 * QuizSpace Main JavaScript
 * Professional, high-performance client-side functionality
 * Version: 1.0.0
 */

// Global QuizSpace Application Object
window.QuizSpace = window.QuizSpace || {
    version: '1.0.0',
    debug: false,

    // Configuration
    config: {
        apiUrl: '/api',
        animationDuration: 300,
        toastDuration: 5000,
        scrollOffset: 80
    },

    // State management
    state: {
        isLoading: false,
        user: null,
        notifications: []
    },

    // Initialize application
    init: function() {
        this.log('QuizSpace Application Initializing...');

        // Initialize core components
        this.initializeNavigation();
        this.initializeAnimations();
        this.initializeTooltips();
        this.initializeForms();
        this.initializeCounters();
        this.initializeModals();
        this.initializeScrollEffects();

        this.log('QuizSpace Application Initialized Successfully!');
    },

    // Logging utility
    log: function(message, type = 'info') {
        if (this.debug) {
            console[type](`[QuizSpace] ${message}`);
        }
    },

    // Error handling
    handleError: function(error, context = '') {
        this.log(`Error in ${context}: ${error.message}`, 'error');
        if (this.utils && this.utils.showToast) {
            this.utils.showToast('একটি সমস্যা হয়েছে। দয়া করে আবার চেষ্টা করুন।', 'error');
        }
    }
};

// Enhanced Navigation functionality
QuizSpace.initializeNavigation = function() {
    this.log('Initializing Navigation...');

    try {
        // Mobile menu toggle with animation
        const navbarToggler = document.querySelector('.navbar-toggler');
        const navbarCollapse = document.querySelector('.navbar-collapse');

        if (navbarToggler && navbarCollapse) {
            navbarToggler.addEventListener('click', function() {
                navbarCollapse.classList.toggle('show');

                // Animate hamburger icon
                this.classList.toggle('active');
            });
        }

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.navbar') && navbarCollapse && navbarCollapse.classList.contains('show')) {
                navbarCollapse.classList.remove('show');
                if (navbarToggler) {
                    navbarToggler.classList.remove('active');
                }
            }
        });

        // Enhanced smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const target = document.getElementById(targetId);

                if (target) {
                    const offsetTop = target.offsetTop - QuizSpace.config.scrollOffset;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });

                    // Close mobile menu if open
                    if (navbarCollapse && navbarCollapse.classList.contains('show')) {
                        navbarCollapse.classList.remove('show');
                        if (navbarToggler) {
                            navbarToggler.classList.remove('active');
                        }
                    }
                }
            });
        });

        // Active navigation highlighting
        this.updateActiveNavigation();

        // Navbar scroll effect
        this.initializeNavbarScrollEffect();

    } catch (error) {
        this.handleError(error, 'Navigation');
    }
};

// Navbar scroll effect
QuizSpace.initializeNavbarScrollEffect = function() {
    const navbar = document.getElementById('mainNavbar');
    if (!navbar) return;

    let lastScrollTop = 0;
    let isScrolling = false;

    window.addEventListener('scroll', function() {
        if (!isScrolling) {
            window.requestAnimationFrame(function() {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                // Add/remove scrolled class
                if (scrollTop > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }

                // Hide/show navbar on scroll (optional)
                if (scrollTop > lastScrollTop && scrollTop > 200) {
                    // Scrolling down
                    navbar.style.transform = 'translateY(-100%)';
                } else {
                    // Scrolling up
                    navbar.style.transform = 'translateY(0)';
                }

                lastScrollTop = scrollTop;
                isScrolling = false;
            });

            isScrolling = true;
        }
    });
};

// Update active navigation based on current page
QuizSpace.updateActiveNavigation = function() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link[data-page]');

    navLinks.forEach(link => {
        const page = link.dataset.page;
        link.classList.remove('active');

        if ((currentPath === '/' && page === 'home') ||
            (currentPath.includes(page) && page !== 'home')) {
            link.classList.add('active');
        }
    });
};

// Enhanced Animation functionality
QuizSpace.initializeAnimations = function() {
    this.log('Initializing Animations...');

    try {
        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const animationObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;

                    // Add appropriate animation class
                    if (element.classList.contains('animate-on-scroll')) {
                        element.classList.add('fade-in-up');
                    }

                    if (element.classList.contains('slide-up')) {
                        element.classList.add('fade-in-up');
                    }

                    // Stagger animations for multiple elements
                    if (element.dataset.delay) {
                        element.style.animationDelay = element.dataset.delay;
                    }

                    animationObserver.unobserve(element);
                }
            });
        }, observerOptions);

        // Observe elements with animation classes
        const animatedElements = document.querySelectorAll('.card, .stat-item, .feature-card, .animate-on-scroll, .slide-up');
        animatedElements.forEach(el => {
            animationObserver.observe(el);
        });

        // Initialize parallax effects
        this.initializeParallax();

    } catch (error) {
        this.handleError(error, 'Animations');
    }
};

// Parallax scrolling effects
QuizSpace.initializeParallax = function() {
    const parallaxElements = document.querySelectorAll('.parallax');

    if (parallaxElements.length === 0) return;

    let isScrolling = false;

    window.addEventListener('scroll', function() {
        if (!isScrolling) {
            window.requestAnimationFrame(function() {
                const scrollTop = window.pageYOffset;

                parallaxElements.forEach(element => {
                    const speed = element.dataset.speed || 0.5;
                    const yPos = -(scrollTop * speed);
                    element.style.transform = `translateY(${yPos}px)`;
                });

                isScrolling = false;
            });

            isScrolling = true;
        }
    });
};

// Enhanced Counter animation
QuizSpace.initializeCounters = function() {
    this.log('Initializing Counters...');

    try {
        const counters = document.querySelectorAll('.counter, .stat-number');
        if (counters.length === 0) return;

        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateCounter(entry.target);
                    counterObserver.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        });

        counters.forEach(counter => {
            counterObserver.observe(counter);
        });

    } catch (error) {
        this.handleError(error, 'Counters');
    }
};

// Enhanced counter animation function
QuizSpace.animateCounter = function(element) {
    const target = parseInt(element.getAttribute('data-target') || element.textContent.replace(/[^\d]/g, ''));
    const prefix = element.getAttribute('data-prefix') || '';
    const suffix = element.getAttribute('data-suffix') || '';
    const duration = parseInt(element.getAttribute('data-duration')) || 2000;
    const separator = element.getAttribute('data-separator') !== 'false';

    let current = 0;
    const increment = target / (duration / 16);

    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }

        let displayValue = Math.floor(current);
        if (separator) {
            displayValue = displayValue.toLocaleString();
        }

        // Handle currency formatting
        if (element.textContent.includes('৳')) {
            element.textContent = '৳' + displayValue + '+';
        } else if (prefix || suffix) {
            element.textContent = prefix + displayValue + suffix;
        } else {
            element.textContent = displayValue + '+';
        }
    }, 16);

    // Add animation class for visual effect
    element.classList.add('counting');
    setTimeout(() => {
        element.classList.remove('counting');
    }, duration);
};

// Enhanced Tooltip initialization
QuizSpace.initializeTooltips = function() {
    this.log('Initializing Tooltips...');

    try {
        // Initialize Bootstrap tooltips if available
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl, {
                    trigger: 'hover focus',
                    delay: { show: 500, hide: 100 }
                });
            });
        }

        // Initialize Bootstrap popovers if available
        if (typeof bootstrap !== 'undefined' && bootstrap.Popover) {
            const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl, {
                    trigger: 'click',
                    html: true
                });
            });
        }

    } catch (error) {
        this.handleError(error, 'Tooltips');
    }
};

// Enhanced Form functionality
QuizSpace.initializeForms = function() {
    this.log('Initializing Forms...');

    try {
        // Enhanced form validation
        const forms = document.querySelectorAll('form');

        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }

                // Show loading state
                const submitButton = form.querySelector('button[type="submit"]');
                if (submitButton && this.utils) {
                    this.utils.setLoading(submitButton, true);
                }
            });

            // Real-time validation with debouncing
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                let timeout;

                input.addEventListener('input', () => {
                    this.clearFieldError(input);
                    clearTimeout(timeout);
                    timeout = setTimeout(() => {
                        this.validateField(input);
                    }, 300);
                });

                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
            });
        });

        // Enhanced file upload handling
        const fileInputs = document.querySelectorAll('input[type="file"]');
        fileInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                this.handleFileUpload(e.target);
            });
        });

    } catch (error) {
        this.handleError(error, 'Forms');
    }
};

// Enhanced Form validation
QuizSpace.validateForm = function(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');

    inputs.forEach(input => {
        if (!this.validateField(input)) {
            isValid = false;
        }
    });

    return isValid;
};

// Enhanced Field validation
QuizSpace.validateField = function(field) {
    const value = field.value.trim();
    const type = field.type;
    let isValid = true;
    let message = '';

    // Required validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        message = field.dataset.requiredMessage || 'এই ক্ষেত্রটি পূরণ করা আবশ্যক';
    }

    // Email validation
    else if (type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            message = field.dataset.emailMessage || 'দয়া করে সঠিক ইমেইল ঠিকানা দিন';
        }
    }

    // Password validation
    else if (type === 'password' && value) {
        const minLength = parseInt(field.dataset.minLength) || 6;
        if (value.length < minLength) {
            isValid = false;
            message = field.dataset.passwordMessage || `পাসওয়ার্ড কমপক্ষে ${minLength} অক্ষরের হতে হবে`;
        }
    }

    // Phone validation (Bangladesh format)
    else if (type === 'tel' && value) {
        const phoneRegex = /^(\+88)?01[3-9]\d{8}$/;
        if (!phoneRegex.test(value)) {
            isValid = false;
            message = field.dataset.phoneMessage || 'দয়া করে সঠিক ফোন নম্বর দিন (যেমন: 01700000000)';
        }
    }

    // URL validation
    else if (type === 'url' && value) {
        try {
            new URL(value);
        } catch {
            isValid = false;
            message = field.dataset.urlMessage || 'দয়া করে সঠিক URL দিন';
        }
    }

    // Number validation
    else if (type === 'number' && value) {
        const min = field.getAttribute('min');
        const max = field.getAttribute('max');
        const numValue = parseFloat(value);

        if (min && numValue < parseFloat(min)) {
            isValid = false;
            message = `মান কমপক্ষে ${min} হতে হবে`;
        } else if (max && numValue > parseFloat(max)) {
            isValid = false;
            message = `মান সর্বোচ্চ ${max} হতে পারে`;
        }
    }

    // Password confirmation
    else if (field.name === 'password_confirmation' && value) {
        const passwordField = field.form.querySelector('input[name="password"]');
        if (passwordField && value !== passwordField.value) {
            isValid = false;
            message = field.dataset.confirmMessage || 'পাসওয়ার্ড মিলছে না';
        }
    }

    // Show/hide error
    if (isValid) {
        this.clearFieldError(field);
    } else {
        this.showFieldError(field, message);
    }

    return isValid;
};

// Show field error
QuizSpace.showFieldError = function(field, message) {
    this.clearFieldError(field);

    field.classList.add('is-invalid');

    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;

    field.parentNode.appendChild(errorDiv);
};

// Clear field error
QuizSpace.clearFieldError = function(field) {
    field.classList.remove('is-invalid');

    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
};

// File upload handling
QuizSpace.handleFileUpload = function(input) {
    const files = input.files;
    const maxSize = parseInt(input.dataset.maxSize) || 5 * 1024 * 1024; // 5MB default
    const allowedTypes = input.dataset.allowedTypes ? input.dataset.allowedTypes.split(',') : [];

    for (let file of files) {
        // Check file size
        if (file.size > maxSize) {
            this.showFieldError(input, `ফাইলের সাইজ ${maxSize / (1024 * 1024)}MB এর চেয়ে বড় হতে পারবে না`);
            input.value = '';
            return false;
        }

        // Check file type
        if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
            this.showFieldError(input, 'এই ধরনের ফাইল সাপোর্ট করা হয় না');
            input.value = '';
            return false;
        }
    }

    this.clearFieldError(input);
    return true;
};

// Utility functions
const Utils = {
    // Show notification
    showNotification: function(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    },
    
    // Format currency
    formatCurrency: function(amount) {
        return '৳' + parseFloat(amount).toLocaleString('en-BD', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        });
    },
    
    // Format date
    formatDate: function(date) {
        return new Date(date).toLocaleDateString('en-BD', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    },
    
    // Debounce function
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// Enhanced Modal functionality
QuizSpace.initializeModals = function() {
    this.log('Initializing Modals...');

    try {
        // Initialize Bootstrap modals if available
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                new bootstrap.Modal(modal, {
                    backdrop: 'static',
                    keyboard: true
                });
            });
        }

    } catch (error) {
        this.handleError(error, 'Modals');
    }
};

// Scroll effects
QuizSpace.initializeScrollEffects = function() {
    this.log('Initializing Scroll Effects...');

    try {
        // Back to top button
        const backToTopButton = document.createElement('button');
        backToTopButton.innerHTML = '<i class="fas fa-arrow-up"></i>';
        backToTopButton.className = 'btn btn-primary position-fixed';
        backToTopButton.style.cssText = 'bottom: 20px; right: 20px; z-index: 1000; display: none; border-radius: 50%; width: 50px; height: 50px;';
        backToTopButton.setAttribute('aria-label', 'Back to top');

        document.body.appendChild(backToTopButton);

        // Show/hide back to top button
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.style.display = 'block';
            } else {
                backToTopButton.style.display = 'none';
            }
        });

        // Smooth scroll to top
        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

    } catch (error) {
        this.handleError(error, 'Scroll Effects');
    }
};

// Make QuizSpace globally available
window.QuizSpace = QuizSpace;

// Handle page visibility change
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        QuizSpace.log('Page is hidden');
    } else {
        QuizSpace.log('Page is visible');
    }
});

// Handle online/offline status
window.addEventListener('online', function() {
    if (QuizSpace.utils) {
        QuizSpace.utils.showToast('আপনি আবার অনলাইনে এসেছেন!', 'success');
    }
});

window.addEventListener('offline', function() {
    if (QuizSpace.utils) {
        QuizSpace.utils.showToast('আপনি অফলাইনে আছেন। কিছু ফিচার কাজ নাও করতে পারে।', 'warning');
    }
});
