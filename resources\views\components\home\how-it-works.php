<?php
/**
 * How It Works Section - QuizSpace User Journey
 * Professional step-by-step guide for earning process
 */

// Dynamic how-it-works data
$howItWorksData = [
    'title' => 'How QuizSpace Works',
    'subtitle' => 'Start earning money in just 4 simple steps. It\'s that easy!',
    'steps' => [
        [
            'number' => '01',
            'icon' => 'fas fa-user-plus',
            'title' => 'Sign Up Free',
            'description' => 'Create your account in under 2 minutes. No hidden fees, completely free to join.',
            'color' => 'primary',
            'highlight' => 'Free Registration'
        ],
        [
            'number' => '02',
            'icon' => 'fas fa-brain',
            'title' => 'Take Quizzes',
            'description' => 'Answer fun and engaging quizzes on various topics. Earn coins for every correct answer.',
            'color' => 'success',
            'highlight' => 'Earn Coins'
        ],
        [
            'number' => '03',
            'icon' => 'fas fa-share-alt',
            'title' => 'Social Activities',
            'description' => 'Share content, engage with community, and participate in social activities to earn more.',
            'color' => 'warning',
            'highlight' => 'Social Rewards'
        ],
        [
            'number' => '04',
            'icon' => 'fas fa-money-bill-wave',
            'title' => 'Withdraw Money',
            'description' => 'Convert your coins to cash and withdraw via bKash, Nagad, or bank transfer instantly.',
            'color' => 'danger',
            'highlight' => 'Instant Payout'
        ]
    ],
    'benefits' => [
        'No investment required',
        'Instant payments',
        'Multiple earning methods',
        '24/7 customer support'
    ]
];
?>

<!-- How It Works Section -->
<section class="py-5 bg-white" id="how-it-works">
    <div class="container">
        <!-- Section Header -->
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="display-5 fw-bold mb-3"><?= htmlspecialchars($howItWorksData['title']) ?></h2>
                <p class="lead text-muted mb-4"><?= htmlspecialchars($howItWorksData['subtitle']) ?></p>
                <div class="d-flex justify-content-center">
                    <div class="bg-primary" style="width: 60px; height: 4px; border-radius: 2px;"></div>
                </div>
            </div>
        </div>

        <!-- Steps -->
        <div class="row g-4 mb-5">
            <?php foreach ($howItWorksData['steps'] as $index => $step): ?>
            <div class="col-lg-3 col-md-6">
                <div class="step-card text-center h-100 position-relative">
                    <!-- Step Number -->
                    <div class="step-number position-relative mb-4">
                        <div class="step-circle bg-<?= $step['color'] ?> text-white rounded-circle d-inline-flex align-items-center justify-content-center mx-auto position-relative" style="width: 80px; height: 80px; z-index: 2;">
                            <i class="<?= $step['icon'] ?>" style="font-size: 1.8rem;"></i>
                        </div>
                        <div class="step-number-bg position-absolute top-50 start-50 translate-middle bg-<?= $step['color'] ?> bg-opacity-10 rounded-circle" style="width: 120px; height: 120px; z-index: 1;"></div>

                        <!-- Connector Line (except last step) -->
                        <?php if ($index < count($howItWorksData['steps']) - 1): ?>
                        <div class="step-connector d-none d-lg-block position-absolute top-50 start-100 translate-middle-y bg-<?= $step['color'] ?> bg-opacity-20" style="width: 100px; height: 2px; z-index: 0;"></div>
                        <?php endif; ?>
                    </div>

                    <!-- Step Content -->
                    <div class="step-content">
                        <span class="badge bg-<?= $step['color'] ?> bg-opacity-10 text-<?= $step['color'] ?> mb-3 px-3 py-2">
                            <?= htmlspecialchars($step['highlight']) ?>
                        </span>
                        <h4 class="fw-bold mb-3"><?= htmlspecialchars($step['title']) ?></h4>
                        <p class="text-muted mb-0"><?= htmlspecialchars($step['description']) ?></p>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Benefits Row -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="bg-light rounded-4 p-4">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <h4 class="fw-bold mb-3">Why Choose Our Platform?</h4>
                            <div class="row">
                                <?php foreach ($howItWorksData['benefits'] as $benefit): ?>
                                <div class="col-md-6 mb-2">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span><?= htmlspecialchars($benefit) ?></span>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <div class="col-lg-4 text-center">
                            <a href="/register" class="btn btn-primary btn-lg px-4 py-3 fw-bold">
                                <i class="fas fa-rocket me-2"></i>Start Earning Now
                            </a>
                            <p class="small text-muted mt-2 mb-0">Join 5,000+ active earners</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- How It Works Styles -->
<style>
.step-card {
    transition: all 0.3s ease;
    padding: 2rem 1rem;
}

.step-card:hover {
    transform: translateY(-10px);
}

.step-circle {
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.step-card:hover .step-circle {
    transform: scale(1.1);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.step-number-bg {
    transition: all 0.3s ease;
}

.step-card:hover .step-number-bg {
    transform: translate(-50%, -50%) scale(1.1);
}

.step-connector {
    transition: all 0.3s ease;
}

.step-card:hover + .step-card .step-connector {
    background-color: var(--bs-primary) !important;
    opacity: 0.5;
}

/* Animation for step cards */
.step-card {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

<?php foreach ($howItWorksData['steps'] as $index => $step): ?>
.step-card:nth-child(<?= $index + 1 ?>) {
    animation-delay: <?= $index * 0.2 ?>s;
}
<?php endforeach; ?>

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .step-connector {
        display: none !important;
    }
}

@media (max-width: 768px) {
    .step-card {
        margin-bottom: 2rem;
    }

    .step-circle {
        width: 60px !important;
        height: 60px !important;
    }

    .step-number-bg {
        width: 90px !important;
        height: 90px !important;
    }
}
</style>