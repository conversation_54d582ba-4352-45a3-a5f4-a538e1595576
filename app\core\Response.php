<?php

namespace App\Core;

/**
 * Response Class - QuizSpace
 * Professional HTTP response handling
 */
class Response
{
    /**
     * @var int HTTP status code
     */
    private $statusCode = 200;

    /**
     * @var array HTTP headers
     */
    private $headers = [];

    /**
     * Send JSON response
     */
    public function json($data, $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * Render a view
     */
    public function view($viewName, $data = [], $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: text/html; charset=UTF-8');

        $viewFile = VIEWS_PATH . '/' . str_replace('.', '/', $viewName) . '.php';

        if (!file_exists($viewFile)) {
            throw new \Exception("View file {$viewFile} not found");
        }

        // Extract data for view
        extract($data);

        // Include the view file
        include $viewFile;
        exit;
    }

    /**
     * Send HTML response
     */
    public function html($content, $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: text/html; charset=UTF-8');
        echo $content;
        exit;
    }

    /**
     * Redirect to another URL
     */
    public function redirect($url, $statusCode = 302)
    {
        http_response_code($statusCode);
        header('Location: ' . $url);
        exit;
    }
}