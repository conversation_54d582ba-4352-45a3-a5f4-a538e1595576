<?php
/**
 * Testimonials Section - Dynamic and SEO Optimized
 */

// Dynamic testimonials data (in real app, fetch from database)
$testimonialsData = [
    [
        'name' => 'রহিম আহমেদ',
        'location' => 'ঢাকা',
        'avatar' => '/public/assets/images/avatars/user1.jpg',
        'rating' => 5,
        'text' => 'JobSpace এ মাসে ৳2000+ আয় করছি। কুইজ খেলে এবং সোশ্যাল একটিভিটি করে সহজেই টাকা আয় করা যায়।',
        'earning' => '৳2,500',
        'duration' => '3 months'
    ],
    [
        'name' => 'ফাতেমা খাতুন',
        'location' => 'চট্টগ্রাম',
        'avatar' => '/public/assets/images/avatars/user2.jpg',
        'rating' => 5,
        'text' => 'অসাধারণ প্ল্যাটফর্ম! ঘরে বসে কুইজ খেলে টাকা আয় করতে পারছি। পেমেন্ট ও তাৎক্ষণিক পেয়ে যাই।',
        'earning' => '৳1,800',
        'duration' => '2 months'
    ],
    [
        'name' => 'করিম উদ্দিন',
        'location' => 'সিলেট',
        'avatar' => '/public/assets/images/avatars/user3.jpg',
        'rating' => 5,
        'text' => 'সত্যিই কাজ করে! প্রতিদিন ১-২ ঘন্টা সময় দিয়ে ভালো পরিমাণ টাকা আয় করতে পারছি।',
        'earning' => '৳3,200',
        'duration' => '4 months'
    ]
];
?>

<!-- Testimonials Section -->
<section class="py-5 bg-light" id="testimonials">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-6 fw-bold text-dark">What Our Users Say</h2>
            <p class="lead text-muted">Real stories from real people earning money on JobSpace</p>
        </div>
        
        <div class="row g-4">
            <?php foreach ($testimonialsData as $index => $testimonial): ?>
            <div class="col-lg-4">
                <div class="testimonial-card h-100 p-4 bg-white rounded-3 shadow-sm border-0">
                    <!-- Rating -->
                    <div class="rating mb-3">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                            <i class="fas fa-star <?= $i <= $testimonial['rating'] ? 'text-warning' : 'text-muted' ?>"></i>
                        <?php endfor; ?>
                    </div>
                    
                    <!-- Testimonial Text -->
                    <blockquote class="mb-4">
                        <p class="text-dark fst-italic">"<?= htmlspecialchars($testimonial['text']) ?>"</p>
                    </blockquote>
                    
                    <!-- User Info -->
                    <div class="d-flex align-items-center">
                        <div class="avatar me-3">
                            <img src="<?= htmlspecialchars($testimonial['avatar']) ?>" 
                                 alt="<?= htmlspecialchars($testimonial['name']) ?>"
                                 class="rounded-circle"
                                 width="50" height="50"
                                 onerror="this.src='/public/assets/images/default-avatar.png'">
                        </div>
                        <div class="user-info flex-grow-1">
                            <h6 class="mb-0 fw-semibold text-dark"><?= htmlspecialchars($testimonial['name']) ?></h6>
                            <small class="text-muted"><?= htmlspecialchars($testimonial['location']) ?></small>
                        </div>
                        <div class="earning-badge">
                            <span class="badge bg-success fs-6"><?= htmlspecialchars($testimonial['earning']) ?></span>
                            <small class="d-block text-muted text-center"><?= htmlspecialchars($testimonial['duration']) ?></small>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
