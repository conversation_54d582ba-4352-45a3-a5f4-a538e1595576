<?php
/**
 * Testimonials Section - QuizSpace User Success Stories
 * Professional testimonials with real user experiences
 */

// Dynamic testimonials data (in real app, fetch from database)
$testimonialsData = [
    'title' => 'Success Stories from Our Community',
    'subtitle' => 'Real people, real earnings, real success stories from QuizSpace users',
    'testimonials' => [
        [
            'name' => 'রহিম আহমেদ',
            'location' => 'ঢাকা, বাংলাদেশ',
            'avatar' => '/public/assets/images/avatars/user1.jpg',
            'rating' => 5,
            'text' => 'QuizSpace এ যোগ দেওয়ার পর আমার জীবন পরিবর্তন হয়ে গেছে! প্রতিদিন কুইজ খেলে এবং সোশ্যাল একটিভিটি করে মাসে ৳5000+ আয় করছি। সত্যিই অবিশ্বাস্য!',
            'earning' => '৳5,200',
            'duration' => '6 months',
            'activity' => 'Quiz Expert',
            'verified' => true
        ],
        [
            'name' => 'ফাতেমা খাতুন',
            'location' => 'চট্টগ্রাম, বাংলাদেশ',
            'avatar' => '/public/assets/images/avatars/user2.jpg',
            'rating' => 5,
            'text' => 'ঘরে বসে থেকেই QuizSpace এর মাধ্যমে আয় করতে পারছি। কুইজ খেলা আমার প্যাশন ছিল, এখন সেটাই আমার আয়ের উৎস! পেমেন্ট সিস্টেম খুবই দ্রুত।',
            'earning' => '৳3,800',
            'duration' => '4 months',
            'activity' => 'Social Influencer',
            'verified' => true
        ],
        [
            'name' => 'করিম উদ্দিন',
            'location' => 'সিলেট, বাংলাদেশ',
            'avatar' => '/public/assets/images/avatars/user3.jpg',
            'rating' => 5,
            'text' => 'প্রথমে বিশ্বাস করতে পারিনি যে কুইজ খেলে টাকা আয় করা যায়। কিন্তু QuizSpace সত্যিই কাজ করে! এখন আমি প্রতিদিন ২-৩ ঘন্টা সময় দিয়ে ভালো আয় করছি।',
            'earning' => '৳4,500',
            'duration' => '5 months',
            'activity' => 'Competition Winner',
            'verified' => true
        ],
        [
            'name' => 'সালমা বেগম',
            'location' => 'রাজশাহী, বাংলাদেশ',
            'avatar' => '/public/assets/images/avatars/user4.jpg',
            'rating' => 5,
            'text' => 'QuizSpace আমার স্বপ্নের প্ল্যাটফর্ম! শিক্ষার্থী হিসেবে এখানে পড়াশোনার পাশাপাশি আয় করতে পারছি। রেফারেল প্রোগ্রাম থেকেও ভালো কমিশন পাচ্ছি।',
            'earning' => '৳2,900',
            'duration' => '3 months',
            'activity' => 'Student Earner',
            'verified' => true
        ],
        [
            'name' => 'আব্দুল কাদের',
            'location' => 'খুলনা, বাংলাদেশ',
            'avatar' => '/public/assets/images/avatars/user5.jpg',
            'rating' => 5,
            'text' => 'অবসরের পর QuizSpace আমার জন্য আশীর্বাদ! এখানে আমার জ্ঞান কাজে লাগিয়ে আয় করতে পারছি। কমিউনিটি খুবই সাহায্যকারী এবং বন্ধুত্বপূর্ণ।',
            'earning' => '৳3,100',
            'duration' => '7 months',
            'activity' => 'Knowledge Sharer',
            'verified' => true
        ],
        [
            'name' => 'নাসির হোসেন',
            'location' => 'বরিশাল, বাংলাদেশ',
            'avatar' => '/public/assets/images/avatars/user6.jpg',
            'rating' => 5,
            'text' => 'QuizSpace এর কম্পিটিশনে অংশ নিয়ে বড় পুরস্কার জিতেছি! এটি শুধু আয়ের মাধ্যম নয়, বরং জ্ঞান বৃদ্ধির চমৎকার উপায়ও।',
            'earning' => '৳6,700',
            'duration' => '8 months',
            'activity' => 'Top Performer',
            'verified' => true
        ]
    ],
    'stats' => [
        'total_users' => '5,000+',
        'total_paid' => '৳1,00,000+',
        'avg_rating' => '4.9',
        'success_rate' => '98%'
    ]
];
?>

<!-- Testimonials Section -->
<section class="py-5 bg-light" id="testimonials">
    <div class="container">
        <!-- Section Header -->
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="display-5 fw-bold mb-3"><?= htmlspecialchars($testimonialsData['title']) ?></h2>
                <p class="lead text-muted"><?= htmlspecialchars($testimonialsData['subtitle']) ?></p>
                <div class="d-flex justify-content-center">
                    <div class="bg-primary" style="width: 60px; height: 4px; border-radius: 2px;"></div>
                </div>
            </div>
        </div>

        <!-- Testimonials Grid -->
        <div class="row g-4 mb-5">
            <?php foreach ($testimonialsData['testimonials'] as $index => $testimonial): ?>
            <div class="col-lg-4 col-md-6">
                <div class="testimonial-card h-100 bg-white rounded-4 p-4 shadow-sm border-0 position-relative">
                    <!-- Verified Badge -->
                    <?php if ($testimonial['verified']): ?>
                    <div class="position-absolute top-0 end-0 m-3">
                        <span class="badge bg-success bg-opacity-20 text-success">
                            <i class="fas fa-check-circle me-1"></i>Verified
                        </span>
                    </div>
                    <?php endif; ?>

                    <!-- Rating -->
                    <div class="rating mb-3">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                            <i class="fas fa-star <?= $i <= $testimonial['rating'] ? 'text-warning' : 'text-muted' ?> small"></i>
                        <?php endfor; ?>
                        <span class="ms-2 small text-muted">(<?= $testimonial['rating'] ?>.0)</span>
                    </div>

                    <!-- Testimonial Text -->
                    <blockquote class="mb-4">
                        <p class="text-dark lh-base">"<?= htmlspecialchars($testimonial['text']) ?>"</p>
                    </blockquote>

                    <!-- User Info -->
                    <div class="d-flex align-items-center mb-3">
                        <div class="avatar me-3">
                            <img src="<?= htmlspecialchars($testimonial['avatar']) ?>"
                                 alt="<?= htmlspecialchars($testimonial['name']) ?>"
                                 class="rounded-circle border border-2 border-primary border-opacity-20"
                                 width="50" height="50"
                                 onerror="this.src='/public/assets/images/default-avatar.png'">
                        </div>
                        <div class="user-info flex-grow-1">
                            <h6 class="mb-0 fw-bold text-dark"><?= htmlspecialchars($testimonial['name']) ?></h6>
                            <small class="text-muted d-block"><?= htmlspecialchars($testimonial['location']) ?></small>
                            <small class="text-primary"><?= htmlspecialchars($testimonial['activity']) ?></small>
                        </div>
                    </div>

                    <!-- Earning Stats -->
                    <div class="earning-stats bg-light rounded-3 p-3">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="fw-bold text-success"><?= htmlspecialchars($testimonial['earning']) ?></div>
                                <small class="text-muted">Total Earned</small>
                            </div>
                            <div class="col-6">
                                <div class="fw-bold text-primary"><?= htmlspecialchars($testimonial['duration']) ?></div>
                                <small class="text-muted">Member Since</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Bottom Stats -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="bg-primary bg-opacity-10 rounded-4 p-4">
                    <div class="row text-center g-4">
                        <div class="col-6 col-md-3">
                            <div class="stat-item">
                                <h4 class="fw-bold text-primary mb-1"><?= htmlspecialchars($testimonialsData['stats']['total_users']) ?></h4>
                                <small class="text-muted">Happy Users</small>
                            </div>
                        </div>
                        <div class="col-6 col-md-3">
                            <div class="stat-item">
                                <h4 class="fw-bold text-success mb-1"><?= htmlspecialchars($testimonialsData['stats']['total_paid']) ?></h4>
                                <small class="text-muted">Total Paid</small>
                            </div>
                        </div>
                        <div class="col-6 col-md-3">
                            <div class="stat-item">
                                <h4 class="fw-bold text-warning mb-1"><?= htmlspecialchars($testimonialsData['stats']['avg_rating']) ?></h4>
                                <small class="text-muted">Average Rating</small>
                            </div>
                        </div>
                        <div class="col-6 col-md-3">
                            <div class="stat-item">
                                <h4 class="fw-bold text-info mb-1"><?= htmlspecialchars($testimonialsData['stats']['success_rate']) ?></h4>
                                <small class="text-muted">Success Rate</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials Section Styles -->
<style>
.testimonial-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05) !important;
}

.testimonial-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1) !important;
}

.avatar img {
    transition: transform 0.3s ease;
}

.testimonial-card:hover .avatar img {
    transform: scale(1.1);
}

.stat-item {
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: scale(1.05);
}

/* Animation for testimonial cards */
.testimonial-card {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

<?php foreach ($testimonialsData['testimonials'] as $index => $testimonial): ?>
.testimonial-card:nth-child(<?= $index + 1 ?>) {
    animation-delay: <?= $index * 0.1 ?>s;
}
<?php endforeach; ?>

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .testimonial-card {
        margin-bottom: 1rem;
    }
}
</style>
