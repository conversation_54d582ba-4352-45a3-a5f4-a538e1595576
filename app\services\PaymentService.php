<?php

namespace App\Services;

use App\Core\Database;

/**
 * Payment Service
 * সব ফিচারের জন্য কমন পেমেন্ট সার্ভিস
 */
class PaymentService
{
    private $db;
    private $walletService;

    public function __construct()
    {
        $this->db = Database::getInstance();
        $this->walletService = new WalletService();
    }

    /**
     * Process withdrawal request
     */
    public function processWithdrawal($userId, $amount, $method, $details = [])
    {
        // Validate minimum amount
        if ($amount < 100) {
            throw new \Exception('Minimum withdrawal amount is 100 taka');
        }

        // Check if user has sufficient balance
        if (!$this->walletService->canAfford($userId, $amount)) {
            throw new \Exception('Insufficient balance');
        }

        // Deduct money from wallet
        $result = $this->walletService->deductMoney($userId, $amount, 'withdrawal', "Withdrawal via {$method}");

        // Create withdrawal request
        $withdrawalId = $this->db->insert('withdrawal_requests', [
            'user_id' => $userId,
            'amount' => $amount,
            'method' => $method,
            'details' => json_encode($details),
            'status' => 'pending',
            'created_at' => date('Y-m-d H:i:s')
        ]);

        // Send notification
        $notificationService = new NotificationService();
        $notificationService->create(
            $userId,
            'Withdrawal Request Submitted',
            "Your withdrawal request for {$amount} taka has been submitted and will be processed within 24-48 hours.",
            'info',
            ['withdrawal_id' => $withdrawalId, 'amount' => $amount, 'method' => $method]
        );

        return [
            'success' => true,
            'withdrawal_id' => $withdrawalId,
            'message' => 'Withdrawal request submitted successfully'
        ];
    }

    /**
     * Get withdrawal methods
     */
    public function getWithdrawalMethods()
    {
        return [
            'bkash' => [
                'name' => 'bKash',
                'min_amount' => 100,
                'max_amount' => 25000,
                'fee_percentage' => 1.5,
                'processing_time' => '24 hours',
                'required_fields' => ['phone_number']
            ],
            'nagad' => [
                'name' => 'Nagad',
                'min_amount' => 100,
                'max_amount' => 25000,
                'fee_percentage' => 1.5,
                'processing_time' => '24 hours',
                'required_fields' => ['phone_number']
            ],
            'rocket' => [
                'name' => 'Rocket',
                'min_amount' => 100,
                'max_amount' => 25000,
                'fee_percentage' => 2.0,
                'processing_time' => '48 hours',
                'required_fields' => ['phone_number']
            ],
            'bank' => [
                'name' => 'Bank Transfer',
                'min_amount' => 500,
                'max_amount' => 100000,
                'fee_percentage' => 0,
                'processing_time' => '3-5 business days',
                'required_fields' => ['account_name', 'account_number', 'bank_name', 'branch_name']
            ]
        ];
    }

    /**
     * Calculate withdrawal fee
     */
    public function calculateWithdrawalFee($amount, $method)
    {
        $methods = $this->getWithdrawalMethods();

        if (!isset($methods[$method])) {
            throw new \Exception('Invalid withdrawal method');
        }

        $feePercentage = $methods[$method]['fee_percentage'];
        $fee = ($amount * $feePercentage) / 100;

        return [
            'amount' => $amount,
            'fee' => $fee,
            'net_amount' => $amount - $fee,
            'fee_percentage' => $feePercentage
        ];
    }

    /**
     * Get user withdrawal history
     */
    public function getWithdrawalHistory($userId, $limit = 20)
    {
        return $this->db->fetchAll(
            "SELECT * FROM withdrawal_requests WHERE user_id = ? ORDER BY created_at DESC LIMIT ?",
            [$userId, $limit]
        );
    }

    /**
     * Admin: Get pending withdrawals
     */
    public function getPendingWithdrawals($limit = 50)
    {
        return $this->db->fetchAll(
            "SELECT wr.*, u.name as user_name, u.email as user_email
            FROM withdrawal_requests wr
            JOIN users u ON wr.user_id = u.id
            WHERE wr.status = 'pending'
            ORDER BY wr.created_at ASC
            LIMIT ?",
            [$limit]
        );
    }

    /**
     * Admin: Approve withdrawal
     */
    public function approveWithdrawal($withdrawalId, $adminId, $transactionId = null)
    {
        $withdrawal = $this->db->fetch(
            "SELECT * FROM withdrawal_requests WHERE id = ? AND status = 'pending'",
            [$withdrawalId]
        );

        if (!$withdrawal) {
            throw new \Exception('Withdrawal request not found or already processed');
        }

        // Update withdrawal status
        $this->db->update('withdrawal_requests', [
            'status' => 'approved',
            'processed_by' => $adminId,
            'processed_at' => date('Y-m-d H:i:s'),
            'transaction_id' => $transactionId
        ], 'id = ?', [$withdrawalId]);

        // Send notification to user
        $notificationService = new NotificationService();
        $notificationService->create(
            $withdrawal['user_id'],
            'Withdrawal Approved',
            "Your withdrawal request for {$withdrawal['amount']} taka has been approved and processed.",
            'success',
            ['withdrawal_id' => $withdrawalId, 'amount' => $withdrawal['amount']]
        );

        return true;
    }

    /**
     * Admin: Reject withdrawal
     */
    public function rejectWithdrawal($withdrawalId, $adminId, $reason = '')
    {
        $withdrawal = $this->db->fetch(
            "SELECT * FROM withdrawal_requests WHERE id = ? AND status = 'pending'",
            [$withdrawalId]
        );

        if (!$withdrawal) {
            throw new \Exception('Withdrawal request not found or already processed');
        }

        // Update withdrawal status
        $this->db->update('withdrawal_requests', [
            'status' => 'rejected',
            'processed_by' => $adminId,
            'processed_at' => date('Y-m-d H:i:s'),
            'rejection_reason' => $reason
        ], 'id = ?', [$withdrawalId]);

        // Refund money to user wallet
        $this->walletService->addMoney(
            $withdrawal['user_id'],
            $withdrawal['amount'],
            'refund',
            "Withdrawal refund - {$reason}"
        );

        // Send notification to user
        $notificationService = new NotificationService();
        $notificationService->create(
            $withdrawal['user_id'],
            'Withdrawal Rejected',
            "Your withdrawal request for {$withdrawal['amount']} taka has been rejected. Reason: {$reason}. Money has been refunded to your wallet.",
            'warning',
            ['withdrawal_id' => $withdrawalId, 'amount' => $withdrawal['amount'], 'reason' => $reason]
        );

        return true;
    }
}