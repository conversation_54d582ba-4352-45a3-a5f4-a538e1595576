<?php
/**
 * Contact Page - QuizSpace Customer Support
 * Professional contact page with multiple contact methods
 */

// SEO Meta Data
$pageData = [
    'title' => 'যোগাযোগ করুন - QuizSpace | 24/7 কাস্টমার সাপোর্ট',
    'description' => 'QuizSpace টিমের সাথে যোগাযোগ করুন। আমরা আপনার যেকোনো প্রশ্ন বা সমস্যার সমাধানে সাহায্য করতে প্রস্তুত। 24/7 সাপোর্ট সেবা।',
    'keywords' => 'quizspace contact, customer support, help center, যোগাযোগ, কাস্টমার সাপোর্ট, সাহায্য',
    'canonical' => 'https://quizspace.com/contact',
    'og_title' => 'QuizSpace এর সাথে যোগাযোগ করুন - 24/7 সাপোর্ট',
    'og_description' => 'যেকোনো সাহায্যের জন্য আমাদের সাথে যোগাযোগ করুন। দ্রুত ও কার্যকর সমাধান পাবেন।',
    'og_image' => '/public/assets/images/og-contact.jpg'
];

// Start output buffering
ob_start();
?>

<!-- Contact Hero Section -->
<section class="py-5 bg-gradient-primary text-white position-relative overflow-hidden">
    <!-- Background Elements -->
    <div class="position-absolute w-100 h-100 top-0 start-0" style="z-index: 1; opacity: 0.1;">
        <div class="position-absolute" style="top: 10%; left: 5%;">
            <i class="fas fa-headset" style="font-size: 3rem;"></i>
        </div>
        <div class="position-absolute" style="top: 20%; right: 10%;">
            <i class="fas fa-envelope" style="font-size: 2.5rem;"></i>
        </div>
        <div class="position-absolute" style="bottom: 15%; left: 15%;">
            <i class="fas fa-phone" style="font-size: 2rem;"></i>
        </div>
        <div class="position-absolute" style="bottom: 25%; right: 5%;">
            <i class="fas fa-comments" style="font-size: 2.8rem;"></i>
        </div>
    </div>

    <div class="container position-relative" style="z-index: 2;">
        <div class="row align-items-center min-vh-50">
            <div class="col-lg-8 mx-auto text-center">
                <div class="mb-4">
                    <span class="badge bg-warning text-dark px-3 py-2 rounded-pill mb-3">
                        <i class="fas fa-clock me-1"></i> 24/7 সাপোর্ট সেবা
                    </span>
                </div>
                <h1 class="display-4 fw-bold mb-4">আমাদের সাথে যোগাযোগ করুন</h1>
                <p class="lead mb-4">আমরা সবসময় আপনার সেবায় নিয়োজিত! যেকোনো প্রশ্ন বা সাহায্যের জন্য আমাদের টিমের সাথে যোগাযোগ করুন।</p>

                <!-- Quick Contact Stats -->
                <div class="row text-center mt-5">
                    <div class="col-6 col-md-3">
                        <div class="fw-bold h5">< 1 ঘন্টা</div>
                        <small class="opacity-75">গড় রিপ্লাই সময়</small>
                    </div>
                    <div class="col-6 col-md-3">
                        <div class="fw-bold h5">24/7</div>
                        <small class="opacity-75">সাপোর্ট সেবা</small>
                    </div>
                    <div class="col-6 col-md-3">
                        <div class="fw-bold h5">99%</div>
                        <small class="opacity-75">সমাধানের হার</small>
                    </div>
                    <div class="col-6 col-md-3">
                        <div class="fw-bold h5">5,000+</div>
                        <small class="opacity-75">সন্তুষ্ট গ্রাহক</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Methods Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row g-4">
            <!-- Live Chat -->
            <div class="col-lg-3 col-md-6">
                <div class="contact-method text-center h-100 bg-white rounded-4 p-4 shadow-sm">
                    <div class="contact-icon mb-3">
                        <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fas fa-comments text-primary fa-2x"></i>
                        </div>
                    </div>
                    <h5 class="fw-bold mb-3">লাইভ চ্যাট</h5>
                    <p class="text-muted mb-4">তাৎক্ষণিক সাহায্যের জন্য আমাদের লাইভ চ্যাট ব্যবহার করুন</p>
                    <button class="btn btn-primary btn-sm" onclick="openLiveChat()">
                        <i class="fas fa-comment me-1"></i>চ্যাট শুরু করুন
                    </button>
                </div>
            </div>

            <!-- Email Support -->
            <div class="col-lg-3 col-md-6">
                <div class="contact-method text-center h-100 bg-white rounded-4 p-4 shadow-sm">
                    <div class="contact-icon mb-3">
                        <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fas fa-envelope text-success fa-2x"></i>
                        </div>
                    </div>
                    <h5 class="fw-bold mb-3">ইমেইল সাপোর্ট</h5>
                    <p class="text-muted mb-4">বিস্তারিত সমস্যার জন্য আমাদের ইমেইল করুন</p>
                    <a href="mailto:<EMAIL>" class="btn btn-success btn-sm">
                        <i class="fas fa-envelope me-1"></i>ইমেইল পাঠান
                    </a>
                </div>
            </div>

            <!-- Phone Support -->
            <div class="col-lg-3 col-md-6">
                <div class="contact-method text-center h-100 bg-white rounded-4 p-4 shadow-sm">
                    <div class="contact-icon mb-3">
                        <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fas fa-phone text-warning fa-2x"></i>
                        </div>
                    </div>
                    <h5 class="fw-bold mb-3">ফোন সাপোর্ট</h5>
                    <p class="text-muted mb-4">জরুরি সাহায্যের জন্য আমাদের কল করুন</p>
                    <a href="tel:+8801700000000" class="btn btn-warning btn-sm text-dark">
                        <i class="fas fa-phone me-1"></i>কল করুন
                    </a>
                </div>
            </div>

            <!-- WhatsApp Support -->
            <div class="col-lg-3 col-md-6">
                <div class="contact-method text-center h-100 bg-white rounded-4 p-4 shadow-sm">
                    <div class="contact-icon mb-3">
                        <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fab fa-whatsapp text-info fa-2x"></i>
                        </div>
                    </div>
                    <h5 class="fw-bold mb-3">WhatsApp</h5>
                    <p class="text-muted mb-4">WhatsApp এর মাধ্যমে দ্রুত সাহায্য পান</p>
                    <a href="https://wa.me/8801700000000" class="btn btn-info btn-sm" target="_blank">
                        <i class="fab fa-whatsapp me-1"></i>WhatsApp করুন
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="text-center mb-5">
                    <h2 class="display-6 fw-bold">আমাদের একটি বার্তা পাঠান</h2>
                    <p class="lead text-muted">আপনার প্রশ্ন বা মতামত জানান। আমরা শীঘ্রই উত্তর দেব।</p>
                </div>

                <div class="card shadow-lg border-0 rounded-4">
                    <div class="card-body p-5">
                        <form id="contactForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label fw-semibold">পূর্ণ নাম *</label>
                                    <input type="text" class="form-control form-control-lg" id="name" name="name" placeholder="আপনার নাম লিখুন" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label fw-semibold">ইমেইল ঠিকানা *</label>
                                    <input type="email" class="form-control form-control-lg" id="email" name="email" placeholder="<EMAIL>" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label fw-semibold">ফোন নম্বর</label>
                                    <input type="tel" class="form-control form-control-lg" id="phone" name="phone" placeholder="+880 1700-000000">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="subject" class="form-label fw-semibold">বিষয় *</label>
                                    <select class="form-select form-select-lg" id="subject" name="subject" required>
                                        <option value="">একটি বিষয় নির্বাচন করুন</option>
                                        <option value="general">সাধারণ জিজ্ঞাসা</option>
                                        <option value="technical">টেকনিক্যাল সাপোর্ট</option>
                                        <option value="payment">পেমেন্ট সমস্যা</option>
                                        <option value="account">অ্যাকাউন্ট সমস্যা</option>
                                        <option value="quiz">কুইজ সংক্রান্ত</option>
                                        <option value="feature">নতুন ফিচার অনুরোধ</option>
                                        <option value="bug">বাগ রিপোর্ট</option>
                                        <option value="partnership">পার্টনারশিপ</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="message" class="form-label fw-semibold">বার্তা *</label>
                                <textarea class="form-control" id="message" name="message" rows="6" required placeholder="আপনার প্রশ্ন বা মতামত বিস্তারিত লিখুন..."></textarea>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg px-5 py-3">
                                    <i class="fas fa-paper-plane me-2"></i>বার্তা পাঠান
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-white">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-6 fw-bold">সাধারণ প্রশ্নাবলী</h2>
            <p class="lead text-muted">দ্রুত উত্তরের জন্য এই প্রশ্নগুলো দেখুন</p>
        </div>

        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="accordion" id="contactFAQ">
                    <div class="accordion-item border-0 mb-3 shadow-sm">
                        <h3 class="accordion-header">
                            <button class="accordion-button bg-light rounded-3" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                কিভাবে দ্রুত সাহায্য পেতে পারি?
                            </button>
                        </h3>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#contactFAQ">
                            <div class="accordion-body">
                                দ্রুত সাহায্যের জন্য আমাদের লাইভ চ্যাট ব্যবহার করুন। এটি 24/7 উপলব্ধ এবং তাৎক্ষণিক উত্তর পাবেন।
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item border-0 mb-3 shadow-sm">
                        <h3 class="accordion-header">
                            <button class="accordion-button collapsed bg-light rounded-3" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                পেমেন্ট সমস্যার জন্য কোথায় যোগাযোগ করব?
                            </button>
                        </h3>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#contactFAQ">
                            <div class="accordion-body">
                                পেমেন্ট সংক্রান্ত যেকোনো সমস্যার জন্য আমাদের ইমেইল করুন: <EMAIL> অথবা ফোন করুন: +880 1700-000000
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item border-0 mb-3 shadow-sm">
                        <h3 class="accordion-header">
                            <button class="accordion-button collapsed bg-light rounded-3" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                কত দ্রুত উত্তর পাব?
                            </button>
                        </h3>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#contactFAQ">
                            <div class="accordion-body">
                                আমরা সাধারণত ১ ঘন্টার মধ্যে উত্তর দেই। জটিল সমস্যার ক্ষেত্রে সর্বোচ্চ ২৪ ঘন্টা সময় লাগতে পারে।
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Office Info Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-6 fw-bold">আমাদের অফিস তথ্য</h2>
            <p class="lead text-muted">আমাদের সাথে সরাসরি যোগাযোগের তথ্য</p>
        </div>

        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="office-info text-center h-100 bg-white rounded-4 p-4 shadow-sm">
                    <div class="office-icon mb-3">
                        <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fas fa-envelope text-primary fa-2x"></i>
                        </div>
                    </div>
                    <h5 class="fw-bold mb-3">ইমেইল ঠিকানা</h5>
                    <p class="text-muted mb-2">সাধারণ সাহায্য:</p>
                    <p class="fw-semibold text-primary mb-2"><EMAIL></p>
                    <p class="text-muted mb-2">ব্যবসায়িক যোগাযোগ:</p>
                    <p class="fw-semibold text-primary"><EMAIL></p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="office-info text-center h-100 bg-white rounded-4 p-4 shadow-sm">
                    <div class="office-icon mb-3">
                        <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fas fa-phone text-success fa-2x"></i>
                        </div>
                    </div>
                    <h5 class="fw-bold mb-3">ফোন নম্বর</h5>
                    <p class="text-muted mb-2">কাস্টমার সাপোর্ট:</p>
                    <p class="fw-semibold text-success mb-2">+880 1700-000000</p>
                    <p class="text-muted mb-2">সময়:</p>
                    <p class="fw-semibold text-success">24/7 উপলব্ধ</p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 mx-auto">
                <div class="office-info text-center h-100 bg-white rounded-4 p-4 shadow-sm">
                    <div class="office-icon mb-3">
                        <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fas fa-map-marker-alt text-info fa-2x"></i>
                        </div>
                    </div>
                    <h5 class="fw-bold mb-3">অফিস ঠিকানা</h5>
                    <p class="text-muted mb-2">প্রধান অফিস:</p>
                    <p class="fw-semibold text-info mb-2">ঢাকা, বাংলাদেশ</p>
                    <p class="text-muted mb-2">অফিস সময়:</p>
                    <p class="fw-semibold text-info">সকাল ৯টা - সন্ধ্যা ৬টা</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form Styles and Scripts -->
<style>
.contact-method {
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
}

.contact-method:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1) !important;
}

.contact-icon {
    transition: transform 0.3s ease;
}

.contact-method:hover .contact-icon {
    transform: scale(1.1);
}

.office-info {
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
}

.office-info:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

.office-icon {
    transition: transform 0.3s ease;
}

.office-info:hover .office-icon {
    transform: scale(1.05);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
</style>

<script>
// Live Chat Function
function openLiveChat() {
    // In real implementation, integrate with live chat service like Tawk.to, Intercom, etc.
    QuizSpace.utils.showToast('লাইভ চ্যাট শীঘ্রই চালু হবে! এখনের জন্য ইমেইল বা ফোন ব্যবহার করুন।', 'info');
}

// Contact Form Enhanced Handling
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contactForm');

    if (contactForm) {
        contactForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            const submitButton = this.querySelector('button[type="submit"]');

            // Validation
            if (!data.name || !data.email || !data.subject || !data.message) {
                QuizSpace.utils.showToast('দয়া করে সকল প্রয়োজনীয় ক্ষেত্র পূরণ করুন', 'warning');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(data.email)) {
                QuizSpace.utils.showToast('দয়া করে সঠিক ইমেইল ঠিকানা দিন', 'warning');
                return;
            }

            // Show loading state
            QuizSpace.utils.setLoading(submitButton, true);

            try {
                // Simulate API call (replace with actual implementation)
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Success
                QuizSpace.utils.showToast('আপনার বার্তা সফলভাবে পাঠানো হয়েছে! আমরা শীঘ্রই উত্তর দেব।', 'success');
                this.reset();

            } catch (error) {
                QuizSpace.utils.showToast('বার্তা পাঠাতে সমস্যা হয়েছে। দয়া করে আবার চেষ্টা করুন।', 'error');
            } finally {
                QuizSpace.utils.setLoading(submitButton, false);
            }
        });
    }
});
</script>

<?php
// Get content and prepare for layout
$content = ob_get_clean();
$title = $pageData['title'];
$description = $pageData['description'];
$keywords = $pageData['keywords'];
$canonical = $pageData['canonical'];
$ogData = [
    'title' => $pageData['og_title'],
    'description' => $pageData['og_description'],
    'image' => $pageData['og_image'],
    'type' => 'website',
    'url' => $pageData['canonical']
];

// Include main layout
include __DIR__ . '/../layouts/app.php';
?>