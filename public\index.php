<?php
/**
 * QuizSpace Application Entry Point
 * Professional PHP application with modern architecture
 */

// Error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set timezone
date_default_timezone_set('Asia/Dhaka');

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_PATH', __DIR__);
define('APP_PATH', APP_ROOT . '/app');
define('CONFIG_PATH', APP_ROOT . '/config');
define('VIEWS_PATH', APP_ROOT . '/resources/views');

// Check if autoloader exists
$autoloaderPath = APP_ROOT . '/vendor/autoload.php';
if (!file_exists($autoloaderPath)) {
    // Fallback: Show a simple welcome page
    include __DIR__ . '/welcome.php';
    exit;
}

// Autoloader
require_once $autoloaderPath;

// Load configuration
$configPath = CONFIG_PATH . '/app.php';
if (file_exists($configPath)) {
    $config = require $configPath;
    // Ensure config is an array
    if (!is_array($config)) {
        $config = ['debug' => true];
    }
} else {
    $config = ['debug' => true];
}

// Initialize application
try {
    // Check if core classes exist
    if (!class_exists('App\Core\Router')) {
        throw new Exception('Router class not found. Please check your application structure.');
    }

    // Create router instance
    $router = new App\Core\Router();

    // Load routes
    $routesPath = APP_ROOT . '/routes/web.php';
    if (!file_exists($routesPath)) {
        throw new Exception('Routes file not found.');
    }
    require $routesPath;

    // Get current request URI and method
    $requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $requestMethod = $_SERVER['REQUEST_METHOD'];

    // Remove query string and normalize path
    $requestUri = rtrim($requestUri, '/') ?: '/';

    // Dispatch the request
    $router->dispatch($requestMethod, $requestUri);

} catch (Exception $e) {
    // Error handling
    http_response_code(500);

    // Check if config is array and has debug key
    $isDebug = is_array($config) && isset($config['debug']) && $config['debug'];

    if ($isDebug) {
        // Show detailed error in development
        echo '<!DOCTYPE html><html><head><title>QuizSpace Error</title>';
        echo '<style>body{font-family:Arial,sans-serif;margin:40px;background:#f5f5f5;}';
        echo '.error-container{background:white;padding:30px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}';
        echo '.error-title{color:#dc3545;margin-bottom:20px;}';
        echo '.error-details{background:#f8f9fa;padding:15px;border-radius:5px;margin:10px 0;}';
        echo 'pre{background:#343a40;color:white;padding:15px;border-radius:5px;overflow:auto;}';
        echo '</style></head><body>';
        echo '<div class="error-container">';
        echo '<h1 class="error-title">🚨 QuizSpace Application Error</h1>';
        echo '<div class="error-details"><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</div>';
        echo '<div class="error-details"><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</div>';
        echo '<div class="error-details"><strong>Line:</strong> ' . $e->getLine() . '</div>';
        echo '<h3>Stack Trace:</h3>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
        echo '<p><a href="/test.php" style="color:#007bff;">View Test Page</a> | ';
        echo '<a href="/welcome.php" style="color:#007bff;">View Welcome Page</a></p>';
        echo '</div></body></html>';
    } else {
        // Show generic error in production
        echo '<!DOCTYPE html><html><head><title>QuizSpace - Error</title>';
        echo '<style>body{font-family:Arial,sans-serif;text-align:center;margin-top:100px;}</style>';
        echo '</head><body>';
        echo '<h1>🔧 Something went wrong</h1>';
        echo '<p>We are working to fix this issue. Please try again later.</p>';
        echo '<p><a href="/test.php">Test Page</a></p>';
        echo '</body></html>';
    }

    // Log the error
    error_log('QuizSpace Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
}