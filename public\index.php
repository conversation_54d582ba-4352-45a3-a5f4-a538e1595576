<?php
/**
 * QuizSpace Application Entry Point
 * Professional PHP application with modern architecture
 */

// Error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set timezone
date_default_timezone_set('Asia/Dhaka');

// Define application constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_PATH', __DIR__);
define('APP_PATH', APP_ROOT . '/app');
define('CONFIG_PATH', APP_ROOT . '/config');
define('VIEWS_PATH', APP_ROOT . '/resources/views');

// Autoloader
require_once APP_ROOT . '/vendor/autoload.php';

// Load configuration
$config = require CONFIG_PATH . '/app.php';

// Initialize application
try {
    // Create router instance
    $router = new App\Core\Router();

    // Load routes
    require APP_ROOT . '/routes/web.php';

    // Get current request URI and method
    $requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $requestMethod = $_SERVER['REQUEST_METHOD'];

    // Remove query string and normalize path
    $requestUri = rtrim($requestUri, '/') ?: '/';

    // Dispatch the request
    $router->dispatch($requestMethod, $requestUri);

} catch (Exception $e) {
    // Error handling
    http_response_code(500);

    if ($config['debug']) {
        // Show detailed error in development
        echo '<h1>Application Error</h1>';
        echo '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>';
        echo '<p><strong>Line:</strong> ' . $e->getLine() . '</p>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    } else {
        // Show generic error in production
        echo '<h1>Something went wrong</h1>';
        echo '<p>We are working to fix this issue. Please try again later.</p>';
    }

    // Log the error
    error_log('QuizSpace Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
}