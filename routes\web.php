<?php
/**
 * Web Routes - Simple and Clean
 * Professional step-by-step approach
 */

// Test route to verify router is working
$router->get('/test', function() {
    $response = new App\Core\Response();
    return $response->json(['message' => 'Router is working!', 'timestamp' => date('Y-m-d H:i:s')]);
});

// Home route
$router->get('/', function() {
    $response = new App\Core\Response();
    return $response->view('pages.home');
});

// Public pages
$router->get('/about', function() {
    $response = new App\Core\Response();
    return $response->view('pages.about');
});

$router->get('/contact', function() {
    $response = new App\Core\Response();
    return $response->view('pages.contact');
});

$router->get('/faq', function() {
    $response = new App\Core\Response();
    return $response->view('pages.faq');
});
