<?php
/**
 * Web Routes - QuizSpace Application Routes
 * Professional routing with SEO-friendly URLs
 */

// Test route to verify router is working
$router->get('/test', function() {
    $response = new App\Core\Response();
    return $response->json(['message' => 'QuizSpace Router is working!', 'timestamp' => date('Y-m-d H:i:s')]);
});

// Home route
$router->get('/', function() {
    $response = new App\Core\Response();
    return $response->view('pages.home');
});

// Public Information Pages
$router->get('/about', function() {
    $response = new App\Core\Response();
    return $response->view('pages.about');
});

$router->get('/contact', function() {
    $response = new App\Core\Response();
    return $response->view('pages.contact');
});

$router->get('/faq', function() {
    $response = new App\Core\Response();
    return $response->view('pages.faq');
});

$router->get('/help', function() {
    $response = new App\Core\Response();
    return $response->view('pages.help');
});

$router->get('/privacy', function() {
    $response = new App\Core\Response();
    return $response->view('pages.privacy');
});

$router->get('/terms', function() {
    $response = new App\Core\Response();
    return $response->view('pages.terms');
});

$router->get('/community', function() {
    $response = new App\Core\Response();
    return $response->view('pages.community');
});

// Quiz & Earning Related Pages (Public Preview)
$router->get('/quiz', function() {
    $response = new App\Core\Response();
    return $response->view('pages.quiz-preview');
});

$router->get('/competitions', function() {
    $response = new App\Core\Response();
    return $response->view('pages.competitions');
});

$router->get('/leaderboard', function() {
    $response = new App\Core\Response();
    return $response->view('pages.leaderboard');
});

// Authentication Routes (Public Access)
$router->get('/login', function() {
    $response = new App\Core\Response();
    return $response->view('auth.login');
});

$router->get('/register', function() {
    $response = new App\Core\Response();
    return $response->view('auth.register');
});

$router->get('/forgot-password', function() {
    $response = new App\Core\Response();
    return $response->view('auth.forgot-password');
});

// API Routes for Public Data
$router->get('/api/stats', function() {
    $response = new App\Core\Response();
    return $response->json([
        'total_users' => '5,000+',
        'total_paid' => '৳1,00,000+',
        'quizzes_completed' => '50,000+',
        'user_satisfaction' => '99%',
        'avg_daily_earning' => '৳150',
        'success_rate' => '98%'
    ]);
});

$router->get('/api/testimonials', function() {
    $response = new App\Core\Response();
    return $response->json([
        'testimonials' => [
            [
                'name' => 'রহিম আহমেদ',
                'location' => 'ঢাকা',
                'earning' => '৳5,200',
                'rating' => 5,
                'text' => 'QuizSpace এ যোগ দেওয়ার পর আমার জীবন পরিবর্তন হয়ে গেছে!'
            ],
            [
                'name' => 'ফাতেমা খাতুন',
                'location' => 'চট্টগ্রাম',
                'earning' => '৳3,800',
                'rating' => 5,
                'text' => 'ঘরে বসে থেকেই QuizSpace এর মাধ্যমে আয় করতে পারছি।'
            ]
        ]
    ]);
});

// Newsletter Subscription
$router->post('/newsletter/subscribe', function() {
    $response = new App\Core\Response();
    // In real implementation, save to database
    return $response->json(['success' => true, 'message' => 'Successfully subscribed to newsletter!']);
});

// Contact Form Submission
$router->post('/contact/submit', function() {
    $response = new App\Core\Response();
    // In real implementation, save to database and send email
    return $response->json(['success' => true, 'message' => 'Message sent successfully! We will get back to you soon.']);
});

// Sitemap and SEO
$router->get('/sitemap.xml', function() {
    $response = new App\Core\Response();
    $sitemap = '<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
        <url><loc>https://quizspace.com/</loc><priority>1.0</priority></url>
        <url><loc>https://quizspace.com/about</loc><priority>0.8</priority></url>
        <url><loc>https://quizspace.com/contact</loc><priority>0.7</priority></url>
        <url><loc>https://quizspace.com/faq</loc><priority>0.7</priority></url>
        <url><loc>https://quizspace.com/quiz</loc><priority>0.9</priority></url>
        <url><loc>https://quizspace.com/register</loc><priority>0.9</priority></url>
    </urlset>';

    header('Content-Type: application/xml');
    echo $sitemap;
    exit;
});

$router->get('/robots.txt', function() {
    $robots = "User-agent: *
Allow: /
Disallow: /admin/
Disallow: /api/private/
Sitemap: https://quizspace.com/sitemap.xml";

    header('Content-Type: text/plain');
    echo $robots;
    exit;
});
