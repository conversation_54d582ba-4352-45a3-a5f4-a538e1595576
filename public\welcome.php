<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>স্বাগতম - QuizSpace | কুইজ খেলে টাকা আয় করুন</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
        .welcome-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .logo-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.2);
        }
        .btn-custom {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="welcome-card p-5">
                    <!-- Header -->
                    <div class="text-center mb-5">
                        <div class="logo-animation mb-4">
                            <i class="fas fa-brain text-primary" style="font-size: 4rem;"></i>
                        </div>
                        <h1 class="display-3 fw-bold text-primary mb-3">QuizSpace</h1>
                        <h2 class="h3 text-success mb-4">🎉 স্বাগতম আপনাদের!</h2>
                        <p class="lead text-muted">
                            বাংলাদেশের #1 অনলাইন আয়ের প্ল্যাটফর্ম। কুইজ খেলে, সোশ্যাল একটিভিটি করে প্রতিদিন টাকা আয় করুন!
                        </p>
                    </div>

                    <!-- Features Grid -->
                    <div class="row mb-5">
                        <div class="col-md-6 col-lg-3">
                            <div class="feature-card text-center text-white">
                                <i class="fas fa-brain fa-3x mb-3 text-warning"></i>
                                <h5 class="fw-bold">কুইজ খেলুন</h5>
                                <p class="small">মজার কুইজ খেলে প্রতিদিন আয় করুন</p>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-3">
                            <div class="feature-card text-center text-white">
                                <i class="fas fa-users fa-3x mb-3 text-info"></i>
                                <h5 class="fw-bold">সোশ্যাল একটিভিটি</h5>
                                <p class="small">বন্ধুদের সাথে ইন্টারঅ্যাক্ট করে আয় করুন</p>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-3">
                            <div class="feature-card text-center text-white">
                                <i class="fas fa-coins fa-3x mb-3 text-warning"></i>
                                <h5 class="fw-bold">তাৎক্ষণিক পেমেন্ট</h5>
                                <p class="small">bKash, Nagad এ সাথে সাথে টাকা পান</p>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-3">
                            <div class="feature-card text-center text-white">
                                <i class="fas fa-shield-alt fa-3x mb-3 text-success"></i>
                                <h5 class="fw-bold">১০০% নিরাপদ</h5>
                                <p class="small">সম্পূর্ণ নিরাপদ ও বিশ্বস্ত প্ল্যাটফর্ম</p>
                            </div>
                        </div>
                    </div>

                    <!-- Stats -->
                    <div class="row text-center mb-5">
                        <div class="col-6 col-md-3">
                            <div class="mb-2">
                                <h3 class="fw-bold text-primary">5,000+</h3>
                                <small class="text-muted">সন্তুষ্ট ব্যবহারকারী</small>
                            </div>
                        </div>
                        <div class="col-6 col-md-3">
                            <div class="mb-2">
                                <h3 class="fw-bold text-success">৳1,00,000+</h3>
                                <small class="text-muted">মোট পেমেন্ট</small>
                            </div>
                        </div>
                        <div class="col-6 col-md-3">
                            <div class="mb-2">
                                <h3 class="fw-bold text-warning">50,000+</h3>
                                <small class="text-muted">কুইজ সম্পন্ন</small>
                            </div>
                        </div>
                        <div class="col-6 col-md-3">
                            <div class="mb-2">
                                <h3 class="fw-bold text-info">4.9/5</h3>
                                <small class="text-muted">ব্যবহারকারী রেটিং</small>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="text-center mb-5">
                        <div class="d-flex flex-wrap justify-content-center gap-3">
                            <a href="/test.php" class="btn btn-custom btn-lg text-white">
                                <i class="fas fa-vial me-2"></i>টেস্ট পেজ দেখুন
                            </a>
                            <a href="/" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-home me-2"></i>হোম পেজ
                            </a>
                            <a href="/about" class="btn btn-outline-info btn-lg">
                                <i class="fas fa-info-circle me-2"></i>আমাদের সম্পর্কে
                            </a>
                        </div>
                    </div>

                    <!-- Installation Status -->
                    <div class="alert alert-info text-center">
                        <h5 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>ইনস্টলেশন স্ট্যাটাস
                        </h5>
                        <p class="mb-2">আপনার QuizSpace অ্যাপ্লিকেশন প্রায় প্রস্তুত!</p>
                        <hr>
                        <div class="row text-start">
                            <div class="col-md-6">
                                <p class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>PHP:</strong> <?= PHP_VERSION ?>
                                </p>
                                <p class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Timezone:</strong> <?= date_default_timezone_get() ?>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Views:</strong> <?= file_exists(dirname(__DIR__) . '/resources/views') ? 'Found' : 'Not Found' ?>
                                </p>
                                <p class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Config:</strong> <?= file_exists(dirname(__DIR__) . '/config') ? 'Found' : 'Not Found' ?>
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="text-center border-top pt-4">
                        <p class="text-muted mb-2">
                            <i class="fas fa-heart text-danger me-1"></i>
                            Made with love in Bangladesh
                        </p>
                        <p class="small text-muted mb-0">
                            QuizSpace v1.0.0 | © 2024 All Rights Reserved
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Welcome animation
        document.addEventListener('DOMContentLoaded', function() {
            const card = document.querySelector('.welcome-card');
            card.style.opacity = '0';
            card.style.transform = 'translateY(50px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.8s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 200);

            // Feature cards animation
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, 500 + (index * 100));
            });
        });
    </script>
</body>
</html>
