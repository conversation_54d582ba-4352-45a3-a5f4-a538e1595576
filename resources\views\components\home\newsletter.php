<?php
/**
 * Newsletter Section - QuizSpace Updates & Notifications
 * Professional newsletter subscription with benefits showcase
 */

// Dynamic newsletter data
$newsletterData = [
    'title' => 'Stay Updated with QuizSpace',
    'subtitle' => 'Get the latest earning opportunities, new quizzes, and exclusive bonuses delivered to your inbox',
    'benefits' => [
        [
            'icon' => 'fas fa-gift',
            'title' => 'Exclusive Bonuses',
            'description' => 'Subscribers get special bonus codes and early access to high-paying quizzes'
        ],
        [
            'icon' => 'fas fa-brain',
            'title' => 'New Quiz Alerts',
            'description' => 'Be the first to know when new quizzes and competitions are available'
        ],
        [
            'icon' => 'fas fa-chart-line',
            'title' => 'Earning Tips',
            'description' => 'Expert strategies and tips to maximize your daily earnings on QuizSpace'
        ]
    ],
    'stats' => [
        'subscribers' => '2,500+',
        'avg_bonus' => '৳50',
        'frequency' => 'Weekly'
    ]
];
?>

<!-- Newsletter Section -->
<section class="py-5 bg-primary text-white position-relative overflow-hidden" id="newsletter">
    <!-- Background Pattern -->
    <div class="position-absolute w-100 h-100 top-0 start-0" style="z-index: 1; opacity: 0.1;">
        <div class="position-absolute" style="top: 10%; left: 5%;">
            <i class="fas fa-envelope" style="font-size: 3rem;"></i>
        </div>
        <div class="position-absolute" style="top: 20%; right: 10%;">
            <i class="fas fa-bell" style="font-size: 2.5rem;"></i>
        </div>
        <div class="position-absolute" style="bottom: 15%; left: 15%;">
            <i class="fas fa-gift" style="font-size: 2rem;"></i>
        </div>
        <div class="position-absolute" style="bottom: 25%; right: 5%;">
            <i class="fas fa-star" style="font-size: 2.8rem;"></i>
        </div>
    </div>

    <div class="container position-relative" style="z-index: 2;">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="bg-white rounded-4 p-5 shadow-lg">
                    <div class="row align-items-center">
                        <!-- Left Content -->
                        <div class="col-lg-6 text-dark">
                            <div class="mb-4">
                                <div class="d-inline-flex align-items-center justify-content-center rounded-circle bg-primary bg-opacity-10 p-3 mb-3">
                                    <i class="fas fa-envelope text-primary" style="font-size: 1.5rem;"></i>
                                </div>
                                <h3 class="fw-bold mb-3"><?= htmlspecialchars($newsletterData['title']) ?></h3>
                                <p class="text-muted mb-4"><?= htmlspecialchars($newsletterData['subtitle']) ?></p>
                            </div>

                            <!-- Newsletter Form -->
                            <form action="/newsletter/subscribe" method="POST" class="mb-4" id="newsletterForm">
                                <div class="input-group input-group-lg">
                                    <input type="email"
                                           class="form-control border-0 bg-light"
                                           placeholder="আপনার ইমেইল এড্রেস লিখুন"
                                           name="email"
                                           required>
                                    <button class="btn btn-primary px-4" type="submit">
                                        <i class="fas fa-paper-plane me-2"></i>Subscribe
                                    </button>
                                </div>
                                <small class="text-muted mt-2 d-block">
                                    আমরা স্প্যাম করি না। <a href="/privacy" class="text-primary">Privacy Policy</a> দেখুন।
                                </small>
                            </form>

                            <!-- Stats -->
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="fw-bold text-primary"><?= htmlspecialchars($newsletterData['stats']['subscribers']) ?></div>
                                    <small class="text-muted">Subscribers</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-success"><?= htmlspecialchars($newsletterData['stats']['avg_bonus']) ?></div>
                                    <small class="text-muted">Avg Bonus</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-warning"><?= htmlspecialchars($newsletterData['stats']['frequency']) ?></div>
                                    <small class="text-muted">Updates</small>
                                </div>
                            </div>
                        </div>

                        <!-- Right Content - Benefits -->
                        <div class="col-lg-6">
                            <div class="ps-lg-4">
                                <h5 class="fw-bold text-dark mb-4">What You'll Get:</h5>
                                <div class="space-y-3">
                                    <?php foreach ($newsletterData['benefits'] as $benefit): ?>
                                    <div class="d-flex align-items-start mb-3">
                                        <div class="me-3">
                                            <div class="d-inline-flex align-items-center justify-content-center rounded-circle bg-primary bg-opacity-10 p-2">
                                                <i class="<?= $benefit['icon'] ?> text-primary"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="fw-bold text-dark mb-1"><?= htmlspecialchars($benefit['title']) ?></h6>
                                            <p class="text-muted small mb-0"><?= htmlspecialchars($benefit['description']) ?></p>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>

                                <!-- Special Offer -->
                                <div class="mt-4 p-3 bg-warning bg-opacity-10 rounded-3 border border-warning border-opacity-20">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-fire text-warning me-2"></i>
                                        <div>
                                            <small class="fw-bold text-dark">Special Offer!</small>
                                            <small class="d-block text-muted">Subscribe now and get ৳25 bonus instantly</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Section Styles and Scripts -->
<style>
.input-group-lg .form-control {
    border-radius: 12px 0 0 12px !important;
    padding: 1rem 1.5rem;
}

.input-group-lg .btn {
    border-radius: 0 12px 12px 0 !important;
    padding: 1rem 1.5rem;
}

.input-group .form-control:focus {
    box-shadow: none;
    border-color: var(--bs-primary);
}

/* Animation for newsletter section */
.newsletter-section {
    animation: fadeInUp 0.8s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover effects */
.bg-white:hover {
    transform: translateY(-5px);
    transition: all 0.3s ease;
}
</style>

<script>
// Newsletter form submission
document.getElementById('newsletterForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const email = this.querySelector('input[name="email"]').value;
    const button = this.querySelector('button[type="submit"]');
    const originalText = button.innerHTML;

    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Subscribing...';
    button.disabled = true;

    // Simulate API call (replace with actual implementation)
    setTimeout(() => {
        // Show success message
        button.innerHTML = '<i class="fas fa-check me-2"></i>Subscribed!';
        button.classList.remove('btn-primary');
        button.classList.add('btn-success');

        // Reset form
        this.querySelector('input[name="email"]').value = '';

        // Reset button after 3 seconds
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
            button.classList.remove('btn-success');
            button.classList.add('btn-primary');
        }, 3000);

        // Show success notification (you can implement a toast notification here)
        alert('Successfully subscribed! Check your email for confirmation.');
    }, 2000);
});
</script>