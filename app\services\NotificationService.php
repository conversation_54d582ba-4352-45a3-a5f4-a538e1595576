<?php

namespace App\Services;

use App\Core\Database;

/**
 * Notification Service
 * সব ফিচারের জন্য কমন নোটিফিকেশন সার্ভিস
 */
class NotificationService
{
    private $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Create notification
     */
    public function create($userId, $title, $message, $type = 'info', $data = [])
    {
        return $this->db->insert('notifications', [
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => $type, // info, success, warning, error
            'data' => json_encode($data),
            'is_read' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Get user notifications
     */
    public function getUserNotifications($userId, $limit = 20, $unreadOnly = false)
    {
        $sql = "SELECT * FROM notifications WHERE user_id = ?";
        $params = [$userId];

        if ($unreadOnly) {
            $sql .= " AND is_read = 0";
        }

        $sql .= " ORDER BY created_at DESC LIMIT ?";
        $params[] = $limit;

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead($notificationId, $userId = null)
    {
        $sql = "UPDATE notifications SET is_read = 1, read_at = ? WHERE id = ?";
        $params = [date('Y-m-d H:i:s'), $notificationId];

        if ($userId) {
            $sql .= " AND user_id = ?";
            $params[] = $userId;
        }

        return $this->db->query($sql, $params);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead($userId)
    {
        return $this->db->update('notifications', [
            'is_read' => 1,
            'read_at' => date('Y-m-d H:i:s')
        ], 'user_id = ? AND is_read = 0', [$userId]);
    }

    /**
     * Get unread count
     */
    public function getUnreadCount($userId)
    {
        $result = $this->db->fetch(
            "SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0",
            [$userId]
        );

        return (int) $result['count'];
    }

    /**
     * Delete notification
     */
    public function delete($notificationId, $userId = null)
    {
        $sql = "DELETE FROM notifications WHERE id = ?";
        $params = [$notificationId];

        if ($userId) {
            $sql .= " AND user_id = ?";
            $params[] = $userId;
        }

        return $this->db->query($sql, $params);
    }

    /**
     * Quiz completion notification
     */
    public function notifyQuizCompletion($userId, $quizTitle, $score, $reward)
    {
        $title = "Quiz Completed!";
        $message = "You completed '{$quizTitle}' and earned {$reward} taka!";

        return $this->create($userId, $title, $message, 'success', [
            'quiz_title' => $quizTitle,
            'score' => $score,
            'reward' => $reward,
            'action' => 'quiz_completed'
        ]);
    }

    /**
     * Social activity notification
     */
    public function notifySocialActivity($userId, $action, $reward, $details = [])
    {
        $actions = [
            'post_create' => 'Post created',
            'post_like' => 'Post liked',
            'comment' => 'Comment added',
            'follow' => 'User followed'
        ];

        $title = $actions[$action] ?? 'Social Activity';
        $message = "You earned {$reward} taka for {$title}!";

        return $this->create($userId, $title, $message, 'success', array_merge($details, [
            'action' => $action,
            'reward' => $reward
        ]));
    }

    /**
     * Wallet transaction notification
     */
    public function notifyTransaction($userId, $type, $amount, $description)
    {
        $title = $type === 'credit' ? 'Money Added' : 'Money Deducted';
        $message = $type === 'credit'
            ? "You received {$amount} taka. {$description}"
            : "You spent {$amount} taka. {$description}";

        $notificationType = $type === 'credit' ? 'success' : 'info';

        return $this->create($userId, $title, $message, $notificationType, [
            'transaction_type' => $type,
            'amount' => $amount,
            'description' => $description
        ]);
    }

    /**
     * System notification (for all users or specific users)
     */
    public function sendSystemNotification($title, $message, $userIds = null, $type = 'info')
    {
        if ($userIds === null) {
            // Send to all active users
            $users = $this->db->fetchAll("SELECT id FROM users WHERE status = 'active'");
            $userIds = array_column($users, 'id');
        }

        if (!is_array($userIds)) {
            $userIds = [$userIds];
        }

        $results = [];
        foreach ($userIds as $userId) {
            $results[] = $this->create($userId, $title, $message, $type, [
                'system_notification' => true
            ]);
        }

        return $results;
    }