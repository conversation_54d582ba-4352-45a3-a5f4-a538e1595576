<?php
/**
 * FAQ Section - QuizSpace Frequently Asked Questions
 * Professional FAQ with interactive accordion design
 */

// Dynamic FAQ data
$faqData = [
    'title' => 'Frequently Asked Questions',
    'subtitle' => 'Find answers to common questions about earning money on QuizSpace',
    'faqs' => [
        [
            'id' => 'faq1',
            'question' => 'কিভাবে QuizSpace এ অ্যাকাউন্ট তৈরি করব?',
            'answer' => 'খুবই সহজ! উপরের "রেজিস্টার" বাটনে ক্লিক করুন, আপনার ইমেইল ও ফোন নম্বর দিয়ে সাইন আপ করুন। ইমেইল ভেরিফাই করার পর আপনি সাথে সাথে কুইজ খেলে আয় শুরু করতে পারবেন।',
            'category' => 'Getting Started'
        ],
        [
            'id' => 'faq2',
            'question' => 'কুইজ খেলে কত টাকা আয় করা যায়?',
            'answer' => 'আপনার সক্রিয়তার উপর নির্ভর করে প্রতিদিন ৳50-200 এবং মাসে ৳1500-6000 পর্যন্ত আয় করতে পারবেন। নিয়মিত কুইজ খেলে, সোশ্যাল একটিভিটি করে এবং রেফারেল করে আরও বেশি আয় সম্ভব।',
            'category' => 'Earnings'
        ],
        [
            'id' => 'faq3',
            'question' => 'পেমেন্ট কিভাবে পাব এবং কত দিন লাগে?',
            'answer' => 'আমরা bKash, Nagad, Rocket এবং ব্যাংক ট্রান্সফারের মাধ্যমে পেমেন্ট দিয়ে থাকি। ন্যূনতম ৳100 হলেই উইথড্র করতে পারবেন। সাধারণত 24-48 ঘন্টার মধ্যে পেমেন্ট পৌঁছে যায়।',
            'category' => 'Payments'
        ],
        [
            'id' => 'faq4',
            'question' => 'কয়েন এবং ক্যাশের মধ্যে পার্থক্য কি?',
            'answer' => 'কয়েন হলো আমাদের ভার্চুয়াল কারেন্সি যা আপনি কুইজ খেলে এবং সোশ্যাল একটিভিটি করে পাবেন। এই কয়েনগুলো আপনি রিয়েল ক্যাশে কনভার্ট করতে পারবেন। সাধারণত 100 কয়েন = ৳1 টাকা।',
            'category' => 'Coins & Cash'
        ],
        [
            'id' => 'faq5',
            'question' => 'রেফারেল প্রোগ্রাম কিভাবে কাজ করে?',
            'answer' => 'আপনার রেফারেল লিংক শেয়ার করে বন্ধুদের জয়েন করান। প্রতিটি সফল রেফারেলের জন্য ৳50 বোনাস পাবেন এবং তাদের আয়ের 10% আজীবন কমিশন পাবেন। এটি প্যাসিভ ইনকামের দুর্দান্ত উপায়!',
            'category' => 'Referral'
        ],
        [
            'id' => 'faq6',
            'question' => 'কুইজের প্রশ্নগুলো কেমন হয়?',
            'answer' => 'আমাদের কুইজে সাধারণ জ্ঞান, ইতিহাস, বিজ্ঞান, খেলাধুলা, বর্তমান ঘটনা, ইসলামিক জ্ঞান সহ বিভিন্ন বিষয়ের প্রশ্ন থাকে। প্রশ্নগুলো সহজ থেকে কঠিন - সবার জন্য উপযুক্ত।',
            'category' => 'Quiz'
        ],
        [
            'id' => 'faq7',
            'question' => 'দৈনিক কতটি কুইজ খেলতে পারব?',
            'answer' => 'কোনো সীমা নেই! আপনি যত খুশি কুইজ খেলতে পারবেন। তবে প্রতিদিন নতুন কুইজ আপডেট হয় এবং বিশেষ ইভেন্টে বোনাস কুইজ পাওয়া যায়। বেশি খেললে বেশি আয়!',
            'category' => 'Quiz'
        ],
        [
            'id' => 'faq8',
            'question' => 'অ্যাকাউন্ট নিরাপত্তা কেমন?',
            'answer' => 'আমরা ব্যাংক-গ্রেড নিরাপত্তা ব্যবহার করি। আপনার সকল তথ্য এনক্রিপ্টেড এবং সুরক্ষিত। আমরা কখনো আপনার ব্যক্তিগত তথ্য তৃতীয় পক্ষের সাথে শেয়ার করি না।',
            'category' => 'Security'
        ],
        [
            'id' => 'faq9',
            'question' => 'কাস্টমার সাপোর্ট কেমন?',
            'answer' => 'আমাদের ২৪/৭ কাস্টমার সাপোর্ট টিম আছে। লাইভ চ্যাট, ইমেইল, ফোন - যেকোনো মাধ্যমে যোগাযোগ করতে পারেন। সাধারণত ১ ঘন্টার মধ্যে রিপ্লাই পাবেন।',
            'category' => 'Support'
        ]
    ]
];
?>

<!-- FAQ Section -->
<section class="py-5 bg-light" id="faq">
    <div class="container">
        <!-- Section Header -->
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="display-5 fw-bold mb-3"><?= htmlspecialchars($faqData['title']) ?></h2>
                <p class="lead text-muted"><?= htmlspecialchars($faqData['subtitle']) ?></p>
                <div class="d-flex justify-content-center">
                    <div class="bg-primary" style="width: 60px; height: 4px; border-radius: 2px;"></div>
                </div>
            </div>
        </div>

        <!-- FAQ Accordion -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="accordion" id="faqAccordion">
                    <?php foreach ($faqData['faqs'] as $index => $faq): ?>
                    <div class="accordion-item border-0 mb-3 shadow-sm">
                        <h3 class="accordion-header" id="heading<?= $faq['id'] ?>">
                            <button class="accordion-button <?= $index !== 0 ? 'collapsed' : '' ?> bg-white rounded-3 fw-semibold"
                                    type="button"
                                    data-bs-toggle="collapse"
                                    data-bs-target="#collapse<?= $faq['id'] ?>"
                                    aria-expanded="<?= $index === 0 ? 'true' : 'false' ?>"
                                    aria-controls="collapse<?= $faq['id'] ?>">
                                <div class="d-flex align-items-center w-100">
                                    <div class="me-3">
                                        <span class="badge bg-primary bg-opacity-20 text-primary"><?= sprintf('%02d', $index + 1) ?></span>
                                    </div>
                                    <div class="flex-grow-1 text-start">
                                        <?= htmlspecialchars($faq['question']) ?>
                                        <small class="d-block text-muted mt-1"><?= htmlspecialchars($faq['category']) ?></small>
                                    </div>
                                </div>
                            </button>
                        </h3>
                        <div id="collapse<?= $faq['id'] ?>"
                             class="accordion-collapse collapse <?= $index === 0 ? 'show' : '' ?>"
                             aria-labelledby="heading<?= $faq['id'] ?>"
                             data-bs-parent="#faqAccordion">
                            <div class="accordion-body bg-white rounded-bottom-3 pt-0">
                                <div class="ps-5">
                                    <p class="text-muted mb-0 lh-base"><?= htmlspecialchars($faq['answer']) ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Bottom CTA -->
        <div class="row justify-content-center mt-5">
            <div class="col-lg-8 text-center">
                <div class="bg-primary bg-opacity-10 rounded-4 p-4">
                    <h4 class="fw-bold mb-3">Still Have Questions?</h4>
                    <p class="text-muted mb-4">আমাদের সাপোর্ট টিম সবসময় আপনার সেবায় নিয়োজিত</p>
                    <div class="d-flex flex-wrap justify-content-center gap-3">
                        <a href="/contact" class="btn btn-primary">
                            <i class="fas fa-headset me-2"></i>Contact Support
                        </a>
                        <a href="/help" class="btn btn-outline-primary">
                            <i class="fas fa-book me-2"></i>Help Center
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section Styles -->
<style>
.accordion-button {
    border: none !important;
    box-shadow: none !important;
    padding: 1.5rem !important;
}

.accordion-button:not(.collapsed) {
    background-color: #f8f9fa !important;
    color: var(--bs-primary) !important;
}

.accordion-button::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e") !important;
}

.accordion-item {
    border-radius: 12px !important;
    overflow: hidden;
}

.accordion-button:hover {
    background-color: #f8f9fa !important;
}

/* Animation for FAQ items */
.accordion-item {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(20px);
}

<?php foreach ($faqData['faqs'] as $index => $faq): ?>
.accordion-item:nth-child(<?= $index + 1 ?>) {
    animation-delay: <?= $index * 0.1 ?>s;
}
<?php endforeach; ?>

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>