<?php
// Dynamic data for hero section
$heroData = [
    'title' => 'Start Earning Money Online Today!',
    'subtitle' => 'Join thousands of users who are earning money through fun activities like quizzes, social interactions, and more. It\'s simple, engaging, and rewarding!',
    'stats' => [
        ['number' => '৳50,000+', 'label' => 'Total Paid'],
        ['number' => '1,000+', 'label' => 'Active Users'],
        ['number' => '5,000+', 'label' => 'Quizzes Completed']
    ]
];
?>

<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4"><?= htmlspecialchars($heroData['title']) ?></h1>
                <p class="lead mb-4"><?= htmlspecialchars($heroData['subtitle']) ?></p>

                <div class="d-flex gap-3 mb-4">
                    <a href="/register" class="btn btn-light btn-lg">
                        <i class="fas fa-rocket me-2"></i>Get Started Free
                    </a>
                    <a href="/about" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-play me-2"></i>Learn More
                    </a>
                </div>

                <div class="d-flex gap-4 text-center">
                    <?php foreach ($heroData['stats'] as $stat): ?>
                    <div>
                        <div class="h4 mb-0"><?= htmlspecialchars($stat['number']) ?></div>
                        <small><?= htmlspecialchars($stat['label']) ?></small>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <div class="bg-light rounded p-5">
                        <i class="fas fa-coins fa-5x text-primary mb-3"></i>
                        <h3>Start Earning Today!</h3>
                        <p class="text-muted">Join the earning revolution</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

            <!-- Right Content - Search Form -->
            <div class="w-full">
                <div class="bg-white p-8 rounded-2xl shadow-2xl">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">Quick Job Search</h3>
                    <form id="job-search-form" class="space-y-4" action="/jobspace/jobs" method="GET">
                        <div>
                            <label for="keywords" class="block text-sm font-medium text-gray-700 mb-2">Job Title or Keywords</label>
                            <input type="text"
                                   id="keywords"
                                   name="keywords"
                                   placeholder="e.g. Frontend Developer, React, PHP"
                                   class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition duration-300">
                        </div>

                        <div>
                            <label for="location" class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                            <input type="text"
                                   id="location"
                                   name="location"
                                   placeholder="e.g. Dhaka, Remote, Chittagong"
                                   class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition duration-300">
                        </div>

                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                            <select id="category"
                                    name="category"
                                    class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition duration-300">
                                <option value="">All Categories</option>
                                <?php foreach ($heroData['categories'] as $category): ?>
                                <option value="<?= htmlspecialchars($category['value']) ?>">
                                    <?= htmlspecialchars($category['label']) ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <button type="submit"
                                class="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300 shadow-lg">
                            <span class="flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                Search Jobs
                            </span>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
