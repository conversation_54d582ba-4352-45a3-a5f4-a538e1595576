<?php
/**
 * Hero Section - QuizSpace Landing
 * Professional, SEO-optimized, high-performance hero section
 */

// Dynamic data for hero section
$heroData = [
    'title' => 'Earn Real Money Through Fun Quizzes & Social Activities',
    'subtitle' => 'Join QuizSpace - the ultimate platform where knowledge meets rewards! Earn coins and cash through engaging quizzes, social interactions, and exciting challenges. Start your earning journey today!',
    'features' => [
        'Dual earning system (Coins + Cash)',
        'Instant payments & withdrawals',
        'Fun quizzes & social activities',
        'Daily challenges & bonuses'
    ],
    'stats' => [
        ['number' => '৳1,00,000+', 'label' => 'Total Paid', 'icon' => 'fas fa-money-bill-wave'],
        ['number' => '5,000+', 'label' => 'Active Users', 'icon' => 'fas fa-users'],
        ['number' => '50,000+', 'label' => 'Quizzes Completed', 'icon' => 'fas fa-brain'],
        ['number' => '99%', 'label' => 'User Satisfaction', 'icon' => 'fas fa-star']
    ],
    'earning_methods' => [
        ['icon' => 'fas fa-question-circle', 'title' => 'Quiz Challenges', 'desc' => 'Answer questions & earn'],
        ['icon' => 'fas fa-share-alt', 'title' => 'Social Activities', 'desc' => 'Share, like & get rewarded'],
        ['icon' => 'fas fa-gift', 'title' => 'Daily Bonuses', 'desc' => 'Login daily for extra coins'],
        ['icon' => 'fas fa-trophy', 'title' => 'Competitions', 'desc' => 'Win big in tournaments']
    ]
];
?>

<!-- Hero Section with Gradient Background -->
<section class="hero-section position-relative overflow-hidden" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
    <!-- Animated Background Elements -->
    <div class="position-absolute w-100 h-100" style="z-index: 1;">
        <div class="floating-coins">
            <i class="fas fa-coins position-absolute" style="top: 10%; left: 10%; font-size: 2rem; color: rgba(255,255,255,0.1); animation: float 6s ease-in-out infinite;"></i>
            <i class="fas fa-brain position-absolute" style="top: 20%; right: 15%; font-size: 1.5rem; color: rgba(255,255,255,0.1); animation: float 8s ease-in-out infinite reverse;"></i>
            <i class="fas fa-trophy position-absolute" style="bottom: 30%; left: 20%; font-size: 1.8rem; color: rgba(255,255,255,0.1); animation: float 7s ease-in-out infinite;"></i>
            <i class="fas fa-star position-absolute" style="bottom: 20%; right: 10%; font-size: 1.3rem; color: rgba(255,255,255,0.1); animation: float 5s ease-in-out infinite reverse;"></i>
        </div>
    </div>

    <div class="container position-relative" style="z-index: 2;">
        <div class="row align-items-center min-vh-100 py-5">
            <!-- Left Content -->
            <div class="col-lg-6 text-white">
                <div class="mb-4">
                    <span class="badge bg-warning text-dark px-3 py-2 rounded-pill mb-3">
                        <i class="fas fa-fire me-1"></i> #1 Earning Platform in Bangladesh
                    </span>
                </div>

                <h1 class="display-4 fw-bold mb-4 lh-1">
                    <?= htmlspecialchars($heroData['title']) ?>
                </h1>

                <p class="lead mb-4 opacity-90">
                    <?= htmlspecialchars($heroData['subtitle']) ?>
                </p>

                <!-- Key Features -->
                <div class="row mb-4">
                    <?php foreach ($heroData['features'] as $feature): ?>
                    <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-check-circle text-warning me-2"></i>
                            <small class="opacity-90"><?= htmlspecialchars($feature) ?></small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- CTA Buttons -->
                <div class="d-flex flex-wrap gap-3 mb-5">
                    <a href="/register" class="btn btn-warning btn-lg px-4 py-3 fw-bold text-dark">
                        <i class="fas fa-rocket me-2"></i>Start Earning Free
                    </a>
                    <a href="/about" class="btn btn-outline-light btn-lg px-4 py-3">
                        <i class="fas fa-play me-2"></i>Watch Demo
                    </a>
                </div>

                <!-- Stats -->
                <div class="row text-center">
                    <?php foreach ($heroData['stats'] as $stat): ?>
                    <div class="col-6 col-md-3 mb-3">
                        <div class="stat-item">
                            <i class="<?= $stat['icon'] ?> text-warning mb-2" style="font-size: 1.5rem;"></i>
                            <div class="h4 mb-0 fw-bold"><?= htmlspecialchars($stat['number']) ?></div>
                            <small class="opacity-75"><?= htmlspecialchars($stat['label']) ?></small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Right Content - Earning Methods -->
            <div class="col-lg-6">
                <div class="bg-white rounded-4 p-4 shadow-lg">
                    <div class="text-center mb-4">
                        <h3 class="h4 text-dark mb-2">How You Can Earn</h3>
                        <p class="text-muted small">Multiple ways to maximize your income</p>
                    </div>

                    <div class="row g-3">
                        <?php foreach ($heroData['earning_methods'] as $method): ?>
                        <div class="col-6">
                            <div class="earning-method text-center p-3 rounded-3 border h-100">
                                <div class="mb-3">
                                    <i class="<?= $method['icon'] ?> text-primary" style="font-size: 2rem;"></i>
                                </div>
                                <h6 class="fw-bold text-dark mb-2"><?= htmlspecialchars($method['title']) ?></h6>
                                <p class="small text-muted mb-0"><?= htmlspecialchars($method['desc']) ?></p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Quick Start -->
                    <div class="mt-4 p-3 bg-light rounded-3">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h6 class="mb-1 text-dark">Ready to start?</h6>
                                <small class="text-muted">Join thousands earning daily</small>
                            </div>
                            <a href="/register" class="btn btn-primary btn-sm">
                                Join Now <i class="fas fa-arrow-right ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Floating Animation CSS -->
<style>
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.earning-method {
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.8);
}

.earning-method:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    background: white;
}

.stat-item {
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: scale(1.05);
}

.hero-section .btn {
    transition: all 0.3s ease;
}

.hero-section .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}
</style>
