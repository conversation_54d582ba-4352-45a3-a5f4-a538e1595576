<?php
/**
 * CTA Section - QuizSpace Final Call to Action
 * Professional conversion-optimized CTA section
 */

// Dynamic CTA data
$ctaData = [
    'title' => 'আজই শুরু করুন আপনার আয়ের যাত্রা!',
    'subtitle' => 'QuizSpace এ যোগ দিন এবং কুইজ খেলে, সোশ্যাল একটিভিটি করে প্রতিদিন টাকা আয় করুন',
    'features' => [
        ['icon' => 'fas fa-gift', 'text' => 'সম্পূর্ণ ফ্রি রেজিস্ট্রেশন'],
        ['icon' => 'fas fa-bolt', 'text' => 'তাৎক্ষণিক পেমেন্ট'],
        ['icon' => 'fas fa-shield-alt', 'text' => 'কোনো লুকানো ফি নেই'],
        ['icon' => 'fas fa-headset', 'text' => '২৪/৭ কাস্টমার সাপোর্ট'],
        ['icon' => 'fas fa-coins', 'text' => 'ডুয়াল আয় সিস্টেম'],
        ['icon' => 'fas fa-mobile-alt', 'text' => 'মোবাইল ফ্রেন্ডলি']
    ],
    'stats' => [
        'users' => '5,000+',
        'paid' => '৳1,00,000+',
        'rating' => '4.9/5'
    ],
    'urgency' => [
        'text' => 'Limited Time Offer',
        'bonus' => '৳50 Sign-up Bonus',
        'expires' => 'Expires in 24 hours'
    ]
];
?>

<!-- CTA Section -->
<section class="cta-section bg-gradient-primary text-white py-5 position-relative overflow-hidden" id="cta">
    <!-- Background Animation -->
    <div class="position-absolute w-100 h-100 top-0 start-0" style="z-index: 1;">
        <div class="floating-elements">
            <div class="floating-coin position-absolute" style="top: 10%; left: 5%; animation: float 6s ease-in-out infinite;">
                <i class="fas fa-coins text-warning opacity-20" style="font-size: 2rem;"></i>
            </div>
            <div class="floating-coin position-absolute" style="top: 30%; right: 10%; animation: float 8s ease-in-out infinite reverse;">
                <i class="fas fa-trophy text-warning opacity-20" style="font-size: 1.5rem;"></i>
            </div>
            <div class="floating-coin position-absolute" style="bottom: 20%; left: 15%; animation: float 7s ease-in-out infinite;">
                <i class="fas fa-star text-warning opacity-20" style="font-size: 1.8rem;"></i>
            </div>
        </div>
    </div>

    <div class="container position-relative" style="z-index: 2;">
        <div class="row align-items-center">
            <!-- Left Content -->
            <div class="col-lg-7">
                <div class="cta-content">
                    <!-- Urgency Badge -->
                    <div class="mb-4">
                        <span class="badge bg-warning text-dark px-3 py-2 rounded-pill fs-6 fw-bold">
                            <i class="fas fa-fire me-1"></i>
                            <?= htmlspecialchars($ctaData['urgency']['text']) ?> - <?= htmlspecialchars($ctaData['urgency']['bonus']) ?>
                        </span>
                    </div>

                    <h2 class="display-5 fw-bold mb-3"><?= htmlspecialchars($ctaData['title']) ?></h2>
                    <p class="lead mb-4 opacity-90"><?= htmlspecialchars($ctaData['subtitle']) ?></p>

                    <!-- Features Grid -->
                    <div class="features-grid mb-4">
                        <div class="row g-3">
                            <?php foreach ($ctaData['features'] as $feature): ?>
                            <div class="col-md-6 col-lg-4">
                                <div class="d-flex align-items-center">
                                    <div class="me-2">
                                        <i class="<?= $feature['icon'] ?> text-warning"></i>
                                    </div>
                                    <span class="small"><?= htmlspecialchars($feature['text']) ?></span>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Trust Stats -->
                    <div class="trust-stats">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="fw-bold"><?= htmlspecialchars($ctaData['stats']['users']) ?></div>
                                <small class="opacity-75">Happy Users</small>
                            </div>
                            <div class="col-4">
                                <div class="fw-bold"><?= htmlspecialchars($ctaData['stats']['paid']) ?></div>
                                <small class="opacity-75">Total Paid</small>
                            </div>
                            <div class="col-4">
                                <div class="fw-bold"><?= htmlspecialchars($ctaData['stats']['rating']) ?></div>
                                <small class="opacity-75">User Rating</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Content - CTA Buttons -->
            <div class="col-lg-5">
                <div class="cta-card bg-white bg-opacity-10 rounded-4 p-4 backdrop-blur">
                    <div class="text-center">
                        <!-- Main CTA -->
                        <div class="mb-4">
                            <h4 class="fw-bold mb-3">এখনই শুরু করুন!</h4>
                            <a href="/register" class="btn btn-warning btn-lg px-5 py-3 fw-bold text-dark mb-3 d-block cta-primary">
                                <i class="fas fa-rocket me-2"></i>
                                ফ্রি রেজিস্ট্রেশন করুন
                            </a>
                            <small class="text-white-50 d-block mb-3">
                                ২ মিনিটেই অ্যাকাউন্ট তৈরি করুন এবং আয় শুরু করুন
                            </small>
                        </div>

                        <!-- Secondary Actions -->
                        <div class="d-flex gap-2 justify-content-center mb-4">
                            <a href="/about" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-info-circle me-1"></i>আরও জানুন
                            </a>
                            <a href="/demo" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-play me-1"></i>ডেমো দেখুন
                            </a>
                        </div>

                        <!-- Security Badge -->
                        <div class="security-badges">
                            <div class="d-flex justify-content-center align-items-center gap-3 mb-3">
                                <div class="text-center">
                                    <i class="fas fa-shield-alt text-success mb-1"></i>
                                    <small class="d-block">SSL Secured</small>
                                </div>
                                <div class="text-center">
                                    <i class="fas fa-lock text-success mb-1"></i>
                                    <small class="d-block">Data Protected</small>
                                </div>
                                <div class="text-center">
                                    <i class="fas fa-check-circle text-success mb-1"></i>
                                    <small class="d-block">Verified Platform</small>
                                </div>
                            </div>
                        </div>

                        <!-- Countdown Timer -->
                        <div class="countdown-timer bg-danger bg-opacity-20 rounded-3 p-3">
                            <small class="fw-bold">⏰ বোনাস অফার শেষ হবে:</small>
                            <div class="d-flex justify-content-center gap-2 mt-2">
                                <div class="text-center">
                                    <div class="fw-bold" id="hours">23</div>
                                    <small>ঘন্টা</small>
                                </div>
                                <div class="text-center">
                                    <div class="fw-bold" id="minutes">59</div>
                                    <small>মিনিট</small>
                                </div>
                                <div class="text-center">
                                    <div class="fw-bold" id="seconds">59</div>
                                    <small>সেকেন্ড</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section Styles and Scripts -->
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.backdrop-blur {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
}

.cta-primary {
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.cta-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.3);
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.floating-coin {
    animation: float 6s ease-in-out infinite;
}

/* Countdown timer animation */
#hours, #minutes, #seconds {
    font-size: 1.2rem;
    color: #fff;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 0.5rem;
    min-width: 40px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .cta-card {
        margin-top: 2rem;
    }

    .features-grid .col-md-6 {
        margin-bottom: 0.5rem;
    }
}
</style>

<script>
// Countdown Timer
function startCountdown() {
    const countdownDate = new Date().getTime() + (24 * 60 * 60 * 1000); // 24 hours from now

    const timer = setInterval(function() {
        const now = new Date().getTime();
        const distance = countdownDate - now;

        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        document.getElementById("hours").innerHTML = hours.toString().padStart(2, '0');
        document.getElementById("minutes").innerHTML = minutes.toString().padStart(2, '0');
        document.getElementById("seconds").innerHTML = seconds.toString().padStart(2, '0');

        if (distance < 0) {
            clearInterval(timer);
            document.querySelector('.countdown-timer').innerHTML = '<small class="fw-bold text-warning">🎉 Offer Extended! Join Now!</small>';
        }
    }, 1000);
}

// Start countdown when page loads
document.addEventListener('DOMContentLoaded', startCountdown);

// CTA Button Click Tracking
document.querySelector('.cta-primary').addEventListener('click', function() {
    // Track conversion (implement your analytics here)
    console.log('CTA clicked - User proceeding to registration');
});
</script>
