<?php
/**
 * Statistics Section - QuizSpace Platform Metrics
 * Professional statistics showcase with animated counters
 */

// Dynamic statistics data
$statisticsData = [
    'title' => 'QuizSpace by the Numbers',
    'subtitle' => 'Join thousands of satisfied users earning real money every day',
    'stats' => [
        [
            'number' => '1,00,000',
            'prefix' => '৳',
            'suffix' => '+',
            'label' => 'Total Paid Out',
            'description' => 'Real money paid to our users',
            'icon' => 'fas fa-money-bill-wave',
            'color' => 'success',
            'animation_delay' => '0.1s'
        ],
        [
            'number' => '5,000',
            'prefix' => '',
            'suffix' => '+',
            'label' => 'Active Users',
            'description' => 'Users earning daily',
            'icon' => 'fas fa-users',
            'color' => 'primary',
            'animation_delay' => '0.2s'
        ],
        [
            'number' => '50,000',
            'prefix' => '',
            'suffix' => '+',
            'label' => 'Quizzes Completed',
            'description' => 'Knowledge challenges solved',
            'icon' => 'fas fa-brain',
            'color' => 'warning',
            'animation_delay' => '0.3s'
        ],
        [
            'number' => '99',
            'prefix' => '',
            'suffix' => '%',
            'label' => 'User Satisfaction',
            'description' => 'Happy and satisfied users',
            'icon' => 'fas fa-star',
            'color' => 'danger',
            'animation_delay' => '0.4s'
        ],
        [
            'number' => '24',
            'prefix' => '',
            'suffix' => '/7',
            'label' => 'Support Available',
            'description' => 'Round the clock assistance',
            'icon' => 'fas fa-headset',
            'color' => 'info',
            'animation_delay' => '0.5s'
        ],
        [
            'number' => '100',
            'prefix' => '',
            'suffix' => '+',
            'label' => 'Quiz Categories',
            'description' => 'Diverse topics to explore',
            'icon' => 'fas fa-list',
            'color' => 'dark',
            'animation_delay' => '0.6s'
        ]
    ],
    'achievements' => [
        [
            'title' => 'Best Earning Platform 2024',
            'organization' => 'Bangladesh Digital Awards',
            'icon' => 'fas fa-trophy'
        ],
        [
            'title' => 'Most Trusted Platform',
            'organization' => 'User Choice Awards',
            'icon' => 'fas fa-shield-alt'
        ],
        [
            'title' => 'Fastest Growing Platform',
            'organization' => 'Tech Innovation Awards',
            'icon' => 'fas fa-rocket'
        ]
    ]
];
?>

<!-- Statistics Section -->
<section class="py-5 bg-primary text-white position-relative overflow-hidden" id="statistics">
    <!-- Background Pattern -->
    <div class="position-absolute w-100 h-100 top-0 start-0" style="z-index: 1;">
        <div class="position-absolute" style="top: 10%; left: 5%; opacity: 0.1;">
            <i class="fas fa-coins" style="font-size: 3rem;"></i>
        </div>
        <div class="position-absolute" style="top: 20%; right: 10%; opacity: 0.1;">
            <i class="fas fa-chart-line" style="font-size: 2.5rem;"></i>
        </div>
        <div class="position-absolute" style="bottom: 15%; left: 15%; opacity: 0.1;">
            <i class="fas fa-trophy" style="font-size: 2rem;"></i>
        </div>
        <div class="position-absolute" style="bottom: 25%; right: 5%; opacity: 0.1;">
            <i class="fas fa-star" style="font-size: 2.8rem;"></i>
        </div>
    </div>

    <div class="container position-relative" style="z-index: 2;">
        <!-- Section Header -->
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="display-5 fw-bold mb-3"><?= htmlspecialchars($statisticsData['title']) ?></h2>
                <p class="lead opacity-90"><?= htmlspecialchars($statisticsData['subtitle']) ?></p>
                <div class="d-flex justify-content-center">
                    <div class="bg-white" style="width: 60px; height: 4px; border-radius: 2px;"></div>
                </div>
            </div>
        </div>

        <!-- Statistics Grid -->
        <div class="row g-4 mb-5">
            <?php foreach ($statisticsData['stats'] as $index => $stat): ?>
            <div class="col-lg-4 col-md-6">
                <div class="stat-item text-center h-100 p-4" style="animation-delay: <?= $stat['animation_delay'] ?>;">
                    <!-- Icon -->
                    <div class="stat-icon mb-3">
                        <div class="d-inline-flex align-items-center justify-content-center rounded-circle bg-white bg-opacity-20 p-3">
                            <i class="<?= $stat['icon'] ?> text-white" style="font-size: 2rem;"></i>
                        </div>
                    </div>

                    <!-- Number -->
                    <div class="stat-number mb-2">
                        <span class="display-4 fw-bold counter"
                              data-target="<?= str_replace(',', '', $stat['number']) ?>"
                              data-prefix="<?= $stat['prefix'] ?>"
                              data-suffix="<?= $stat['suffix'] ?>">
                            <?= $stat['prefix'] ?>0<?= $stat['suffix'] ?>
                        </span>
                    </div>

                    <!-- Label -->
                    <h5 class="fw-bold mb-2"><?= htmlspecialchars($stat['label']) ?></h5>

                    <!-- Description -->
                    <p class="opacity-75 mb-0"><?= htmlspecialchars($stat['description']) ?></p>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Achievements Section -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="bg-white bg-opacity-10 rounded-4 p-4">
                    <div class="row align-items-center">
                        <div class="col-lg-6">
                            <h4 class="fw-bold mb-3">Our Achievements</h4>
                            <p class="opacity-90 mb-4">Recognized by industry leaders and trusted by thousands</p>
                        </div>
                        <div class="col-lg-6">
                            <div class="row g-3">
                                <?php foreach ($statisticsData['achievements'] as $achievement): ?>
                                <div class="col-md-4 text-center">
                                    <div class="achievement-item p-3">
                                        <i class="<?= $achievement['icon'] ?> text-warning mb-2" style="font-size: 1.5rem;"></i>
                                        <h6 class="fw-bold mb-1 small"><?= htmlspecialchars($achievement['title']) ?></h6>
                                        <p class="small opacity-75 mb-0"><?= htmlspecialchars($achievement['organization']) ?></p>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section Styles and Scripts -->
<style>
.stat-item {
    transition: all 0.3s ease;
    animation: fadeInUp 0.8s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

.stat-item:hover {
    transform: translateY(-10px);
}

.stat-icon {
    transition: transform 0.3s ease;
}

.stat-item:hover .stat-icon {
    transform: scale(1.1);
}

.achievement-item {
    transition: all 0.3s ease;
    border-radius: 12px;
}

.achievement-item:hover {
    background: rgba(255,255,255,0.1);
    transform: translateY(-5px);
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.counter {
    animation: countUp 0.5s ease forwards;
}
</style>

<script>
// Counter Animation
document.addEventListener('DOMContentLoaded', function() {
    const counters = document.querySelectorAll('.counter');
    const options = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const target = parseInt(counter.getAttribute('data-target'));
                const prefix = counter.getAttribute('data-prefix') || '';
                const suffix = counter.getAttribute('data-suffix') || '';

                let current = 0;
                const increment = target / 100;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }

                    // Format number with commas
                    const formattedNumber = Math.floor(current).toLocaleString();
                    counter.textContent = prefix + formattedNumber + suffix;
                }, 20);

                observer.unobserve(counter);
            }
        });
    }, options);

    counters.forEach(counter => {
        observer.observe(counter);
    });
});
</script>