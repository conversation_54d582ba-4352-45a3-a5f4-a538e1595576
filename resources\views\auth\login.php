<?php
$title = 'Login - JobSpace';
$description = 'Login to your JobSpace account and start earning money online.';

ob_start();
?>

<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow-lg border-0">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <h2 class="fw-bold text-primary">Welcome Back!</h2>
                            <p class="text-muted">Login to continue earning money</p>
                        </div>

                        <form method="POST" action="/login" id="loginForm">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password" name="password" placeholder="Enter your password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                        <label class="form-check-label" for="remember">Remember me</label>
                                    </div>
                                </div>
                                <div class="col-6 text-end">
                                    <a href="/forgot-password" class="text-decoration-none">Forgot Password?</a>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary w-100 py-2 mb-3">
                                <i class="fas fa-sign-in-alt me-2"></i>Login to Account
                            </button>

                            <div class="text-center">
                                <p class="mb-0">Don't have an account?
                                    <a href="/register" class="text-primary text-decoration-none fw-bold">Sign Up Free</a>
                                </p>
                            </div>
                        </form>

                        <!-- Social Login (Optional) -->
                        <div class="text-center mt-4">
                            <p class="text-muted mb-3">Or login with</p>
                            <div class="d-flex gap-2 justify-content-center">
                                <button class="btn btn-outline-danger">
                                    <i class="fab fa-google me-2"></i>Google
                                </button>
                                <button class="btn btn-outline-primary">
                                    <i class="fab fa-facebook me-2"></i>Facebook
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Benefits Section -->
                <div class="text-center mt-4">
                    <h6 class="text-muted mb-3">Why Login to JobSpace?</h6>
                    <div class="row g-3">
                        <div class="col-4">
                            <div class="text-center">
                                <i class="fas fa-brain text-primary mb-2"></i>
                                <small class="d-block">Play Quizzes</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center">
                                <i class="fas fa-users text-success mb-2"></i>
                                <small class="d-block">Social Activities</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center">
                                <i class="fas fa-wallet text-warning mb-2"></i>
                                <small class="d-block">Earn Money</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Toggle password visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordInput = document.getElementById('password');
    const icon = this.querySelector('i');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// Form validation
document.getElementById('loginForm').addEventListener('submit', function(e) {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;

    if (!email || !password) {
        e.preventDefault();
        alert('Please fill in all fields');
        return false;
    }

    if (password.length < 6) {
        e.preventDefault();
        alert('Password must be at least 6 characters long');
        return false;
    }
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>