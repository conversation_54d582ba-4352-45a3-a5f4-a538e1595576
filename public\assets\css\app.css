/* QuizSpace Main CSS - Professional Styling */

/* Custom Properties */
:root {
    /* Brand Colors */
    --primary-color: #667eea;
    --primary-dark: #764ba2;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --white: #ffffff;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    --gradient-warning: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    --gradient-danger: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);

    /* Typography */
    --font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-base: 1rem;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;

    /* Spacing */
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;

    /* Shadows */
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --box-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --box-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Transitions */
    --transition: all 0.3s ease;
    --transition-fast: all 0.15s ease;
    --transition-slow: all 0.5s ease;
}

/* Global Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: 1.6;
    color: #333;
    background-color: #fafafa;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-bold);
    margin-bottom: 1rem;
    line-height: 1.2;
    color: #1a202c;
}

.display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
    font-weight: var(--font-weight-extrabold);
    line-height: 1.1;
}

p {
    margin-bottom: 1rem;
    color: #4a5568;
}

.lead {
    font-size: 1.125rem;
    font-weight: var(--font-weight-medium);
    line-height: 1.6;
}

/* Enhanced Button Styles */
.btn {
    border-radius: var(--border-radius);
    font-weight: var(--font-weight-semibold);
    font-size: 0.875rem;
    transition: var(--transition);
    border: none;
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    box-shadow: var(--box-shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
    background: var(--gradient-primary);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--gradient-primary);
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
}

.btn-success {
    background: var(--gradient-success);
    color: white;
    border: none;
}

.btn-warning {
    background: var(--gradient-warning);
    color: #333;
    border: none;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border-radius: var(--border-radius-lg);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: var(--border-radius-sm);
}

/* Enhanced Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-sm);
    transition: var(--transition);
    overflow: hidden;
    background: white;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.card-body {
    padding: 1.5rem;
}

/* Feature Cards */
.feature-card {
    border: 1px solid rgba(0,0,0,0.05);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-sm);
    transition: var(--transition);
    background: white;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--box-shadow-xl);
    border-color: rgba(102, 126, 234, 0.1);
}

/* Enhanced Navigation */
.navbar {
    box-shadow: var(--box-shadow-sm);
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    transition: var(--transition);
    padding: 1rem 0;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(20px);
}

.navbar-brand {
    font-weight: var(--font-weight-extrabold);
    font-size: 1.75rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none !important;
}

.navbar-nav .nav-link {
    font-weight: var(--font-weight-medium);
    color: #555 !important;
    transition: var(--transition);
    position: relative;
    margin: 0 0.5rem;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 50%;
    background: var(--gradient-primary);
    transition: var(--transition);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 100%;
}

/* Hero Section Enhancements */
.hero-section {
    background: var(--gradient-primary);
    color: white;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

/* Hero Section Background Effects */
.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(255,255,255,0.1) 0%, transparent 50%);
    opacity: 0.3;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

/* Floating Animation for Hero Elements */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.floating-elements .floating-coin {
    animation: float 6s ease-in-out infinite;
}

/* Enhanced Stats Section */
.stats-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 80px 0;
}

.stat-item {
    text-align: center;
    padding: 2rem;
    transition: var(--transition);
}

.stat-item:hover {
    transform: scale(1.05);
}

.stat-number {
    font-size: 3rem;
    font-weight: var(--font-weight-extrabold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    line-height: 1;
}

/* Enhanced Form Styles */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e2e8f0;
    padding: 0.875rem 1rem;
    transition: var(--transition);
    font-size: 1rem;
    background: white;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.form-control:hover {
    border-color: #cbd5e0;
}

.form-control-lg {
    padding: 1rem 1.25rem;
    font-size: 1.125rem;
    border-radius: var(--border-radius-lg);
}

.form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e2e8f0;
    padding: 0.875rem 1rem;
    transition: var(--transition);
    background: white;
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.form-select-lg {
    padding: 1rem 1.25rem;
    font-size: 1.125rem;
    border-radius: var(--border-radius-lg);
}

.form-label {
    font-weight: var(--font-weight-semibold);
    color: #374151;
    margin-bottom: 0.5rem;
}

.input-group-text {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-right: none;
    color: #6b7280;
}

/* Enhanced Footer */
.footer {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    color: white;
    padding: 60px 0 20px;
}

.footer a {
    color: #a0aec0;
    text-decoration: none;
    transition: var(--transition);
}

.footer a:hover {
    color: white;
    transform: translateX(2px);
}

.footer h4, .footer h5, .footer h6 {
    color: white;
    margin-bottom: 1rem;
}

.footer .btn {
    border: 1px solid rgba(255,255,255,0.2);
    color: rgba(255,255,255,0.8);
    transition: var(--transition);
}

.footer .btn:hover {
    background: rgba(255,255,255,0.1);
    color: white;
    border-color: rgba(255,255,255,0.3);
}

/* Enhanced Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.text-gradient-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-gradient-primary {
    background: var(--gradient-primary) !important;
}

.bg-gradient-success {
    background: var(--gradient-success) !important;
}

.bg-gradient-warning {
    background: var(--gradient-warning) !important;
}

.bg-gradient-danger {
    background: var(--gradient-danger) !important;
}

/* Enhanced Shadow Utilities */
.shadow-sm {
    box-shadow: var(--box-shadow-sm) !important;
}

.shadow {
    box-shadow: var(--box-shadow) !important;
}

.shadow-md {
    box-shadow: var(--box-shadow-md) !important;
}

.shadow-lg {
    box-shadow: var(--box-shadow-lg) !important;
}

.shadow-xl {
    box-shadow: var(--box-shadow-xl) !important;
}

/* Border Radius Utilities */
.rounded-4 {
    border-radius: 1.5rem !important;
}

.rounded-5 {
    border-radius: 2rem !important;
}

/* Hover Effects */
.hover-lift {
    transition: var(--transition);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.hover-scale {
    transition: var(--transition);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-opacity-100:hover {
    opacity: 1 !important;
    transition: opacity 0.3s ease;
}

/* Animation Classes */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
    40%, 43% { transform: translateY(-10px); }
    70% { transform: translateY(-5px); }
    90% { transform: translateY(-2px); }
}

.fade-in {
    animation: fadeIn 0.6s ease-in;
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.fade-in-down {
    animation: fadeInDown 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

.bounce {
    animation: bounce 1s infinite;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(102, 126, 234, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
    transition: var(--transition);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Selection Styles */
::selection {
    background: var(--primary-color);
    color: white;
}

::-moz-selection {
    background: var(--primary-color);
    color: white;
}

/* Component-Specific Styles */

/* Testimonial Cards */
.testimonial-card {
    transition: var(--transition);
    border: 1px solid rgba(0,0,0,0.05);
    background: white;
}

.testimonial-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--box-shadow-xl);
}

.testimonial-card .avatar img {
    transition: var(--transition);
}

.testimonial-card:hover .avatar img {
    transform: scale(1.1);
}

/* Service Cards */
.service-card {
    transition: var(--transition);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.3);
    border-color: rgba(255,255,255,0.2);
}

.service-overlay {
    transition: var(--transition);
}

.service-card:hover .service-overlay {
    opacity: 1 !important;
}

/* FAQ Accordion */
.accordion-button {
    border: none !important;
    box-shadow: none !important;
    padding: 1.5rem !important;
    font-weight: var(--font-weight-semibold);
}

.accordion-button:not(.collapsed) {
    background-color: #f8f9fa !important;
    color: var(--primary-color) !important;
}

.accordion-button::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e") !important;
}

.accordion-item {
    border-radius: var(--border-radius) !important;
    overflow: hidden;
    margin-bottom: 1rem;
}

.accordion-button:hover {
    background-color: #f8f9fa !important;
}

/* Contact Method Cards */
.contact-method {
    transition: var(--transition);
    border: 1px solid rgba(0,0,0,0.05);
}

.contact-method:hover {
    transform: translateY(-8px);
    box-shadow: var(--box-shadow-xl);
}

.contact-icon {
    transition: var(--transition);
}

.contact-method:hover .contact-icon {
    transform: scale(1.1);
}

/* Newsletter Section */
.newsletter-section {
    animation: fadeInUp 0.8s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

/* CTA Section */
.cta-section {
    position: relative;
    overflow: hidden;
}

.cta-card {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
}

.cta-primary {
    transition: var(--transition);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.cta-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.3);
}

/* Badge Styles */
.badge {
    font-weight: var(--font-weight-semibold);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
}

/* Toast Notifications */
.toast-container {
    z-index: 9999;
}

.toast {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
}

/* Dropdown Menus */
.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    font-weight: var(--font-weight-medium);
}

.dropdown-item:hover {
    background: var(--primary-color);
    color: white;
}

.dropdown-item i {
    width: 20px;
    text-align: center;
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: var(--border-radius-sm);
    background: #e2e8f0;
}

.progress-bar {
    background: var(--gradient-primary);
    border-radius: var(--border-radius-sm);
}

/* Responsive Design Enhancements */
@media (max-width: 1200px) {
    .container {
        max-width: 1140px;
    }
}

@media (max-width: 992px) {
    .hero-section {
        padding: 60px 0;
        min-height: auto;
    }

    .display-4 {
        font-size: 2.5rem;
    }

    .display-5 {
        font-size: 2rem;
    }

    .navbar-nav .nav-link::after {
        display: none;
    }
}

@media (max-width: 768px) {
    .hero-section {
        padding: 40px 0;
    }

    .display-4 {
        font-size: 2rem;
    }

    .display-5 {
        font-size: 1.75rem;
    }

    .display-6 {
        font-size: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .navbar-brand {
        font-size: 1.5rem;
    }

    .feature-card,
    .service-card,
    .testimonial-card,
    .contact-method {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 30px 0;
    }

    .display-4 {
        font-size: 1.75rem;
    }

    .display-5 {
        font-size: 1.5rem;
    }

    .display-6 {
        font-size: 1.25rem;
    }

    .lead {
        font-size: 1rem;
    }

    .btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .card-body {
        padding: 1.25rem;
    }

    .stat-number {
        font-size: 1.75rem;
    }

    .footer {
        padding: 40px 0 20px;
    }

    .footer .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .footer,
    .btn,
    .cta-section {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
    :root {
        --text-color: #e2e8f0;
        --bg-color: #1a202c;
        --card-bg: #2d3748;
    }

    /* Uncomment when implementing dark mode */
    /*
    body {
        background-color: var(--bg-color);
        color: var(--text-color);
    }

    .card {
        background-color: var(--card-bg);
        color: var(--text-color);
    }
    */
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus Styles for Better Accessibility */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .btn-primary {
        background: #000;
        border-color: #000;
        color: #fff;
    }

    .btn-primary:hover {
        background: #333;
        border-color: #333;
    }
}
