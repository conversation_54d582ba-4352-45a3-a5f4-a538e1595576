<?php
$title = 'FAQ - JobSpace';
$description = 'Frequently asked questions about JobSpace. Find answers to common questions about earning money online.';

ob_start();
?>

<!-- FAQ Hero Section -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-4">Frequently Asked Questions</h1>
                <p class="lead">Find answers to common questions about JobSpace</p>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="accordion" id="faqAccordion">
                    <!-- Getting Started -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq1">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                How do I start earning money on JobSpace?
                            </button>
                        </h2>
                        <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>Getting started is easy:</p>
                                <ol>
                                    <li>Sign up for a free account</li>
                                    <li>Complete your profile</li>
                                    <li>Start playing quizzes or engaging in social activities</li>
                                    <li>Earn money for each activity you complete!</li>
                                </ol>
                                <p>You can start earning immediately after registration.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Earnings -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq2">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                How much money can I earn?
                            </button>
                        </h2>
                        <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>Your earnings depend on your activity level:</p>
                                <ul>
                                    <li><strong>Quizzes:</strong> ৳10-20 per quiz (based on your score)</li>
                                    <li><strong>Social Posts:</strong> ৳5 per post creation</li>
                                    <li><strong>Likes:</strong> ৳1 per like</li>
                                    <li><strong>Comments:</strong> ৳2 per comment</li>
                                    <li><strong>Follows:</strong> ৳2 per follow</li>
                                </ul>
                                <p>Active users can earn ৳500-2000+ per month!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Still Have Questions -->
<section class="py-5 bg-light">
    <div class="container text-center">
        <h3 class="mb-4">Still Have Questions?</h3>
        <p class="lead mb-4">Can't find what you're looking for? We're here to help!</p>
        <a href="/contact" class="btn btn-primary btn-lg me-3">
            <i class="fas fa-envelope me-2"></i>Contact Support
        </a>
        <a href="/register" class="btn btn-outline-primary btn-lg">
            <i class="fas fa-rocket me-2"></i>Get Started Now
        </a>
    </div>
</section>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>