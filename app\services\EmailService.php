<?php

namespace App\Services;

use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

/**
 * Email Service
 * সব ফিচারের জন্য কমন ইমেইল সার্ভিস
 */
class EmailService
{
    private $mailer;
    private $config;

    public function __construct()
    {
        $this->loadConfig();
        $this->setupMailer();
    }

    /**
     * Load email configuration
     */
    private function loadConfig()
    {
        $configFile = BASE_PATH . '/config/mail.php';
        if (file_exists($configFile)) {
            $this->config = require $configFile;
        } else {
            // Default configuration
            $this->config = [
                'host' => $_ENV['MAIL_HOST'] ?? 'smtp.gmail.com',
                'port' => $_ENV['MAIL_PORT'] ?? 587,
                'username' => $_ENV['MAIL_USERNAME'] ?? '',
                'password' => $_ENV['MAIL_PASSWORD'] ?? '',
                'encryption' => $_ENV['MAIL_ENCRYPTION'] ?? 'tls',
                'from_email' => $_ENV['MAIL_FROM_EMAIL'] ?? '<EMAIL>',
                'from_name' => $_ENV['MAIL_FROM_NAME'] ?? 'JobSpace'
            ];
        }
    }

    /**
     * Setup PHPMailer
     */
    private function setupMailer()
    {
        $this->mailer = new PHPMailer(true);

        try {
            // Server settings
            $this->mailer->isSMTP();
            $this->mailer->Host = $this->config['host'];
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = $this->config['username'];
            $this->mailer->Password = $this->config['password'];
            $this->mailer->SMTPSecure = $this->config['encryption'];
            $this->mailer->Port = $this->config['port'];

            // Default sender
            $this->mailer->setFrom($this->config['from_email'], $this->config['from_name']);

            // Character set
            $this->mailer->CharSet = 'UTF-8';

        } catch (Exception $e) {
            throw new \Exception("Email configuration failed: " . $e->getMessage());
        }
    }

    /**
     * Send email
     */
    public function send($to, $subject, $body, $isHtml = true)
    {
        try {
            // Reset recipients
            $this->mailer->clearAddresses();
            $this->mailer->clearAttachments();

            // Recipients
            if (is_array($to)) {
                foreach ($to as $email => $name) {
                    if (is_numeric($email)) {
                        $this->mailer->addAddress($name);
                    } else {
                        $this->mailer->addAddress($email, $name);
                    }
                }
            } else {
                $this->mailer->addAddress($to);
            }

            // Content
            $this->mailer->isHTML($isHtml);
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $body;

            if ($isHtml) {
                $this->mailer->AltBody = strip_tags($body);
            }

            $this->mailer->send();
            return true;

        } catch (Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send welcome email (for registration)
     */
    public function sendWelcomeEmail($userEmail, $userName)
    {
        $subject = "Welcome to JobSpace!";
        $body = $this->getEmailTemplate('welcome', [
            'user_name' => $userName,
            'app_name' => 'JobSpace'
        ]);

        return $this->send($userEmail, $subject, $body);
    }

    /**
     * Send quiz completion email
     */
    public function sendQuizCompletionEmail($userEmail, $userName, $quizTitle, $score, $reward)
    {
        $subject = "Quiz Completed - You earned {$reward} taka!";
        $body = $this->getEmailTemplate('quiz_completion', [
            'user_name' => $userName,
            'quiz_title' => $quizTitle,
            'score' => $score,
            'reward' => $reward
        ]);

        return $this->send($userEmail, $subject, $body);
    }

    /**
     * Send password reset email
     */
    public function sendPasswordResetEmail($userEmail, $userName, $resetToken)
    {
        $resetUrl = $_ENV['APP_URL'] . "/reset-password?token=" . $resetToken;

        $subject = "Reset Your Password";
        $body = $this->getEmailTemplate('password_reset', [
            'user_name' => $userName,
            'reset_url' => $resetUrl
        ]);

        return $this->send($userEmail, $subject, $body);
    }

    /**
     * Send withdrawal notification email
     */
    public function sendWithdrawalNotification($userEmail, $userName, $amount, $method)
    {
        $subject = "Withdrawal Request Received - {$amount} taka";
        $body = $this->getEmailTemplate('withdrawal', [
            'user_name' => $userName,
            'amount' => $amount,
            'method' => $method
        ]);

        return $this->send($userEmail, $subject, $body);
    }

    /**
     * Get email template
     */
    private function getEmailTemplate($templateName, $data = [])
    {
        $templateFile = BASE_PATH . "/resources/email/{$templateName}.php";

        if (file_exists($templateFile)) {
            // Extract data to variables
            extract($data);

            // Start output buffering
            ob_start();
            include $templateFile;
            return ob_get_clean();
        }

        // Fallback to simple template
        return $this->getSimpleTemplate($templateName, $data);
    }

    /**
     * Simple email template fallback
     */
    private function getSimpleTemplate($templateName, $data)
    {
        $templates = [
            'welcome' => "
                <h2>Welcome to JobSpace, {$data['user_name']}!</h2>
                <p>Thank you for joining our platform. Start earning money by:</p>
                <ul>
                    <li>Playing quizzes</li>
                    <li>Engaging in social activities</li>
                    <li>Completing tasks</li>
                </ul>
                <p>Happy earning!</p>
            ",
            'quiz_completion' => "
                <h2>Congratulations, {$data['user_name']}!</h2>
                <p>You have completed the quiz: <strong>{$data['quiz_title']}</strong></p>
                <p>Your score: <strong>{$data['score']}</strong></p>
                <p>Reward earned: <strong>{$data['reward']} taka</strong></p>
                <p>Keep playing to earn more!</p>
            ",
            'password_reset' => "
                <h2>Password Reset Request</h2>
                <p>Hello {$data['user_name']},</p>
                <p>You have requested to reset your password. Click the link below:</p>
                <p><a href='{$data['reset_url']}'>Reset Password</a></p>
                <p>This link will expire in 1 hour.</p>
            ",
            'withdrawal' => "
                <h2>Withdrawal Request Received</h2>
                <p>Hello {$data['user_name']},</p>
                <p>We have received your withdrawal request:</p>
                <p>Amount: <strong>{$data['amount']} taka</strong></p>
                <p>Method: <strong>{$data['method']}</strong></p>
                <p>We will process it within 24-48 hours.</p>
            "
        ];

        return $templates[$templateName] ?? "<p>Email template not found.</p>";
    }

    /**
     * Send bulk emails (for notifications)
     */
    public function sendBulk($recipients, $subject, $body)
    {
        $results = [];

        foreach ($recipients as $recipient) {
            $email = is_array($recipient) ? $recipient['email'] : $recipient;
            $results[$email] = $this->send($email, $subject, $body);
        }

        return $results;
    }

    /**
     * Send notification email for any feature
     */
    public function sendNotificationEmail($userEmail, $userName, $title, $message, $actionUrl = null)
    {
        $body = "
            <h2>{$title}</h2>
            <p>Hello {$userName},</p>
            <p>{$message}</p>
        ";

        if ($actionUrl) {
            $body .= "<p><a href='{$actionUrl}' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Take Action</a></p>";
        }

        return $this->send($userEmail, $title, $body);
    }
}