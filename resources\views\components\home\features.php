<?php
/**
 * Features Section - QuizSpace Platform Features
 * Professional, SEO-optimized features showcase
 */

// Dynamic features data
$featuresData = [
    'title' => 'Why Choose QuizSpace?',
    'subtitle' => 'Discover the powerful features that make us the #1 earning platform in Bangladesh',
    'features' => [
        [
            'icon' => 'fas fa-coins',
            'title' => 'Dual Earning System',
            'description' => 'Earn both coins and real cash through our innovative dual reward system. Convert coins to cash anytime!',
            'color' => 'warning'
        ],
        [
            'icon' => 'fas fa-brain',
            'title' => 'Smart Quiz Engine',
            'description' => 'AI-powered quiz system that adapts to your knowledge level and interests for maximum engagement.',
            'color' => 'primary'
        ],
        [
            'icon' => 'fas fa-share-alt',
            'title' => 'Social Earning',
            'description' => 'Earn money through social activities like sharing, commenting, and engaging with the community.',
            'color' => 'success'
        ],
        [
            'icon' => 'fas fa-mobile-alt',
            'title' => 'Mobile Optimized',
            'description' => 'Fully responsive design that works perfectly on all devices. Earn anywhere, anytime!',
            'color' => 'info'
        ],
        [
            'icon' => 'fas fa-shield-alt',
            'title' => 'Secure Payments',
            'description' => 'Bank-grade security for all transactions. Multiple payment methods including bKash, Nagad, and bank transfer.',
            'color' => 'danger'
        ],
        [
            'icon' => 'fas fa-trophy',
            'title' => 'Competitions & Tournaments',
            'description' => 'Participate in daily, weekly, and monthly competitions to win big prizes and bonuses.',
            'color' => 'dark'
        ],
        [
            'icon' => 'fas fa-chart-line',
            'title' => 'Real-time Analytics',
            'description' => 'Track your earnings, performance, and progress with detailed analytics and insights.',
            'color' => 'secondary'
        ],
        [
            'icon' => 'fas fa-users',
            'title' => 'Community Features',
            'description' => 'Connect with other earners, share tips, and build your network in our vibrant community.',
            'color' => 'primary'
        ],
        [
            'icon' => 'fas fa-gift',
            'title' => 'Daily Bonuses',
            'description' => 'Login daily to claim bonuses, streak rewards, and special promotional offers.',
            'color' => 'warning'
        ]
    ]
];
?>

<!-- Features Section -->
<section class="py-5 bg-light" id="features">
    <div class="container">
        <!-- Section Header -->
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="display-5 fw-bold mb-3"><?= htmlspecialchars($featuresData['title']) ?></h2>
                <p class="lead text-muted"><?= htmlspecialchars($featuresData['subtitle']) ?></p>
                <div class="d-flex justify-content-center">
                    <div class="bg-primary" style="width: 60px; height: 4px; border-radius: 2px;"></div>
                </div>
            </div>
        </div>

        <!-- Features Grid -->
        <div class="row g-4">
            <?php foreach ($featuresData['features'] as $index => $feature): ?>
            <div class="col-lg-4 col-md-6">
                <div class="feature-card h-100 bg-white rounded-4 p-4 shadow-sm border-0 position-relative overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="position-absolute top-0 end-0 opacity-10">
                        <i class="<?= $feature['icon'] ?>" style="font-size: 4rem; color: var(--bs-<?= $feature['color'] ?>);"></i>
                    </div>

                    <!-- Content -->
                    <div class="position-relative">
                        <!-- Icon -->
                        <div class="feature-icon mb-3">
                            <div class="d-inline-flex align-items-center justify-content-center rounded-circle bg-<?= $feature['color'] ?> bg-opacity-10 p-3">
                                <i class="<?= $feature['icon'] ?> text-<?= $feature['color'] ?>" style="font-size: 1.5rem;"></i>
                            </div>
                        </div>

                        <!-- Title -->
                        <h5 class="fw-bold mb-3 text-dark"><?= htmlspecialchars($feature['title']) ?></h5>

                        <!-- Description -->
                        <p class="text-muted mb-0 lh-base"><?= htmlspecialchars($feature['description']) ?></p>
                    </div>

                    <!-- Hover Effect -->
                    <div class="feature-overlay position-absolute top-0 start-0 w-100 h-100 bg-<?= $feature['color'] ?> bg-opacity-5 opacity-0 transition-all"></div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Bottom CTA -->
        <div class="row justify-content-center mt-5">
            <div class="col-lg-6 text-center">
                <div class="bg-primary bg-opacity-10 rounded-4 p-4">
                    <h4 class="fw-bold mb-3">Ready to Experience These Features?</h4>
                    <p class="text-muted mb-4">Join thousands of users already earning with QuizSpace</p>
                    <a href="/register" class="btn btn-primary btn-lg px-4">
                        <i class="fas fa-rocket me-2"></i>Get Started Now
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section Styles -->
<style>
.feature-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05) !important;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1) !important;
}

.feature-card:hover .feature-overlay {
    opacity: 1 !important;
}

.feature-icon {
    transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
}

.transition-all {
    transition: all 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .feature-card {
        margin-bottom: 1rem;
    }
}

/* Animation for feature cards */
.feature-card {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

<?php foreach ($featuresData['features'] as $index => $feature): ?>
.feature-card:nth-child(<?= $index + 1 ?>) {
    animation-delay: <?= $index * 0.1 ?>s;
}
<?php endforeach; ?>

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>